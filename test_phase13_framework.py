#!/usr/bin/env python3
"""
Test Phase 13 Framework Without ComfyUI
Tests the validation framework using existing panels.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_emotion_extraction():
    """Test Phase 7: Emotion Extraction."""
    
    print("Testing Phase 7: Emotion Extraction")
    print("-" * 40)
    
    try:
        from scripts.emotion_extractor import EmotionExtractor
        
        extractor = EmotionExtractor()
        
        # Test with sample dialogue
        test_dialogue = [
            "I must find the ancient temple!",
            "What is this mysterious sword?", 
            "The guardians are awakening...",
            "I will prove my worth!",
            "The power flows through me now.",
            "Enemies approach! We must fight!"
        ]
        
        results = extractor.extract_from_dialogue_list(test_dialogue)
        
        print(f"Processed {len(results)} dialogue lines")
        for result in results[:3]:  # Show first 3
            print(f"  '{result['line']}' -> {result['emotion']} ({result['confidence']})")
        
        # Save results
        output_dir = Path("outputs/emotion_outputs")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        emotion_data = {
            "test_timestamp": datetime.now().isoformat(),
            "dialogue_emotions": results
        }
        
        output_file = output_dir / "emotion_labels.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(emotion_data, f, indent=2)
        
        print(f"Results saved to: {output_file}")
        print("Phase 7: COMPLETE")
        return True
        
    except Exception as e:
        print(f"Phase 7 failed: {e}")
        return False

def test_local_flow_checker():
    """Test local flow checker functionality."""
    
    print("\nTesting Local Flow Checker")
    print("-" * 40)
    
    try:
        from scripts.local_flow_checker import LocalFlowChecker
        
        checker = LocalFlowChecker()
        print("Local flow checker initialized successfully")
        
        # Look for existing panels to test
        panel_dirs = [
            "outputs/manga_panels",
            "outputs/test_panels_original",
            "outputs/quality_manga_20250601_202543/chapter_01"
        ]
        
        test_panels = []
        for panel_dir in panel_dirs:
            panel_path = Path(panel_dir)
            if panel_path.exists():
                panels = list(panel_path.glob("*.png"))
                if len(panels) >= 2:
                    test_panels = panels[:2]
                    print(f"Found test panels in: {panel_dir}")
                    break
        
        if test_panels:
            # Test pair analysis
            result = checker.analyze_panel_pair(str(test_panels[0]), str(test_panels[1]))
            print(f"Panel pair analysis: {result.get('success', False)}")
            print(f"Confidence: {result.get('confidence', 0.0):.3f}")
            return True
        else:
            print("No test panels found, but checker initialized successfully")
            return True
            
    except Exception as e:
        print(f"Local flow checker failed: {e}")
        return False

def test_coherence_analyzer():
    """Test coherence analyzer integration."""
    
    print("\nTesting Coherence Analyzer")
    print("-" * 40)
    
    try:
        from scripts.coherence_analyzer import CoherenceAnalyzer
        
        analyzer = CoherenceAnalyzer(use_local_flow_checker=True)
        print("Coherence analyzer initialized successfully")
        print(f"Local flow checker enabled: {analyzer.use_local_flow_checker}")
        return True
        
    except Exception as e:
        print(f"Coherence analyzer failed: {e}")
        return False

def test_validation_framework():
    """Test the validation framework without real panels."""
    
    print("\nTesting Validation Framework")
    print("-" * 40)
    
    try:
        from phase13_real_validation import Phase13RealValidator
        
        validator = Phase13RealValidator()
        
        # Test prompt loading
        enhanced_prompts, base_prompts = validator.load_real_prompts()
        
        if enhanced_prompts and base_prompts:
            print(f"Loaded {len(enhanced_prompts)} enhanced prompts")
            print(f"Loaded {len(base_prompts)} base prompts")
            
            # Show sample prompts
            print("\nSample enhanced prompt:")
            print(f"  {enhanced_prompts[0]['enhanced_prompt'][:100]}...")
            
            return True
        else:
            print("No prompts loaded - run extract_real_prompts.py first")
            return False
            
    except Exception as e:
        print(f"Validation framework failed: {e}")
        return False

def create_mock_panels():
    """Create mock panels for testing by copying existing panels."""
    
    print("\nCreating Mock Panels for Testing")
    print("-" * 40)
    
    # Find existing panels
    source_dirs = [
        "outputs/manga_panels",
        "outputs/test_panels_original",
        "outputs/quality_manga_20250601_202543/chapter_01",
        "outputs/quality_manga_20250601_202543/chapter_02"
    ]
    
    source_panels = []
    for source_dir in source_dirs:
        source_path = Path(source_dir)
        if source_path.exists():
            panels = list(source_path.glob("*.png"))
            if panels:
                source_panels.extend(panels)
                if len(source_panels) >= 12:  # Need 6 base + 6 enhanced
                    break
    
    if len(source_panels) < 6:
        print(f"Not enough source panels found: {len(source_panels)}")
        return False
    
    # Create mock directories
    base_dir = Path("outputs/base_panels")
    enhanced_dir = Path("outputs/enhanced_panels")
    
    base_dir.mkdir(parents=True, exist_ok=True)
    enhanced_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy panels as mock base and enhanced
    import shutil
    
    for i in range(6):
        if i < len(source_panels):
            # Base panels (first 6)
            base_target = base_dir / f"base_panel_{i+1:02d}.png"
            shutil.copy2(source_panels[i], base_target)
            
            # Enhanced panels (offset by 3 to get different panels)
            enhanced_source_idx = (i + 3) % len(source_panels)
            enhanced_target = enhanced_dir / f"enhanced_panel_{i+1:02d}.png"
            shutil.copy2(source_panels[enhanced_source_idx], enhanced_target)
    
    base_count = len(list(base_dir.glob("*.png")))
    enhanced_count = len(list(enhanced_dir.glob("*.png")))
    
    print(f"Created {base_count} mock base panels")
    print(f"Created {enhanced_count} mock enhanced panels")
    
    return base_count >= 6 and enhanced_count >= 6

def run_mock_validation():
    """Run validation with mock panels."""
    
    print("\nRunning Mock Validation")
    print("-" * 40)
    
    try:
        from phase13_real_validation import Phase13RealValidator
        
        validator = Phase13RealValidator()
        
        # Check for mock panels
        base_panels, enhanced_panels = validator.check_panel_availability()
        
        if not base_panels or not enhanced_panels:
            print("Mock panels not available")
            return False
        
        print(f"Found {len(base_panels)} base panels")
        print(f"Found {len(enhanced_panels)} enhanced panels")
        
        # Run analysis on mock panels
        base_result = validator.analyze_real_panels(base_panels, "base")
        enhanced_result = validator.analyze_real_panels(enhanced_panels, "enhanced")
        
        if base_result and enhanced_result:
            print(f"Base analysis score: {base_result.get('coherence_score', 0.0):.3f}")
            print(f"Enhanced analysis score: {enhanced_result.get('coherence_score', 0.0):.3f}")
            
            # Create comparison
            enhanced_prompts, base_prompts = validator.load_real_prompts()
            if enhanced_prompts and base_prompts:
                comparison = validator.compare_real_results(
                    base_result, enhanced_result, base_prompts, enhanced_prompts
                )
                
                print(f"Score delta: {comparison.get('scores', {}).get('delta', 0.0):+.3f}")
                return True
        
        return False
        
    except Exception as e:
        print(f"Mock validation failed: {e}")
        return False

def main():
    """Run complete Phase 13 framework test."""
    
    print("Phase 13 Framework Test (No ComfyUI Required)")
    print("=" * 60)
    
    # Test individual components
    emotion_ok = test_emotion_extraction()
    flow_checker_ok = test_local_flow_checker()
    coherence_ok = test_coherence_analyzer()
    framework_ok = test_validation_framework()
    
    # Create mock panels and test validation
    mock_panels_ok = create_mock_panels()
    mock_validation_ok = run_mock_validation() if mock_panels_ok else False
    
    # Summary
    print("\n" + "=" * 60)
    print("Phase 13 Framework Test Summary:")
    print(f"  Phase 7 (Emotion Extraction): {'PASS' if emotion_ok else 'FAIL'}")
    print(f"  Local Flow Checker: {'PASS' if flow_checker_ok else 'FAIL'}")
    print(f"  Coherence Analyzer: {'PASS' if coherence_ok else 'FAIL'}")
    print(f"  Validation Framework: {'PASS' if framework_ok else 'FAIL'}")
    print(f"  Mock Panel Creation: {'PASS' if mock_panels_ok else 'FAIL'}")
    print(f"  Mock Validation: {'PASS' if mock_validation_ok else 'FAIL'}")
    
    # Determine overall status
    core_components_ok = emotion_ok and flow_checker_ok and coherence_ok and framework_ok
    
    if core_components_ok:
        print("\nPHASE 7: COMPLETE")
        if mock_validation_ok:
            print("PHASE 13: FRAMEWORK READY (Real panels needed for completion)")
        else:
            print("PHASE 13: FRAMEWORK ISSUES (Needs debugging)")
    else:
        print("\nPHASE 7/13: INCOMPLETE (Core components need fixing)")
    
    return 0 if core_components_ok else 1

if __name__ == "__main__":
    exit(main())
