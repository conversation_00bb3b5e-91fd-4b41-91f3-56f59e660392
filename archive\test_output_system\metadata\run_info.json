{"run_name": "test_output_system", "created_at": "2025-06-02T20:31:24.188178", "structure": {"panels": {"base": "Base panels (standard prompts)", "enhanced": "Enhanced panels (layout-enhanced prompts)"}, "validation": {"logs": "Validation execution logs", "scores": "Coherence and quality scores", "reports": "Human-readable validation reports"}, "emotions": "Emotion extraction results", "metadata": "Run metadata and prompt mappings"}, "panel_mappings": {"base": {"1": {"filename": "panel_001_ninja_discovers_temple.png", "prompt_summary": "ninja discovers temple", "created_at": "2025-06-02T20:31:24.188178"}}, "enhanced": {"1": {"filename": "panel_001_ninja_discovers_temple_enhanced.png", "prompt_summary": "ninja discovers temple enhanced", "created_at": "2025-06-02T20:31:24.188178"}}}, "validation_results": {"scores": {"file": "validation\\scores\\validation_scores_20250602_203124.json", "saved_at": "2025-06-02T20:31:24.189179"}}, "emotion_results": {"file": "emotions\\emotion_labels.json", "saved_at": "2025-06-02T20:31:24.190179", "total_lines": 6}}