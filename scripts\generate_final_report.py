#!/usr/bin/env python3
"""
Final Report Generator

Generates comprehensive HTML and Markdown reports for manga evaluation results.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.final_evaluator import ScoreAggregator
from scripts.bias_calibrator import BiasCalibrator
from scripts.visual_qa_agent import VisualQAAgent


class FinalReportGenerator:
    """Generates comprehensive evaluation reports."""
    
    def __init__(self):
        self.aggregator = ScoreAggregator()
        self.calibrator = BiasCalibrator()
        self.vqa_agent = VisualQAAgent()
    
    def generate_comprehensive_analysis(self, manga_dir: str) -> Dict[str, Any]:
        """Generate complete analysis with all components."""
        print(f"📊 Generating comprehensive analysis for: {manga_dir}")
        
        # Step 1: Aggregate scores
        print("   🔍 Aggregating scores...")
        aggregated_scores = self.aggregator.aggregate_scores(manga_dir)
        
        # Step 2: Bias calibration
        print("   🎛️  Calibrating for bias...")
        panel_scores = aggregated_scores.get("technical_scores", {}).get("panels", {})
        bias_analysis = self.calibrator.detect_systematic_bias(panel_scores)
        calibrated_scores = self.calibrator.calibrate_scores(panel_scores, bias_analysis)
        calibration_report = self.calibrator.generate_calibration_report(
            panel_scores, calibrated_scores, bias_analysis
        )
        
        # Step 3: VQA analysis for flagged panels
        print("   🧠 Running VQA analysis...")
        flagged_panels = calibration_report.get("flagged_panels", [])
        vqa_analyses = {}
        
        if flagged_panels:
            # Limit to top 5 most problematic panels to avoid API overuse
            top_flagged = sorted(flagged_panels, 
                               key=lambda x: (len(x.get("flags", [])), -x.get("composite_score", 1.0)))[:5]
            vqa_analyses = self.vqa_agent.batch_analyze_panels(top_flagged)
        
        # Combine all results
        comprehensive_analysis = {
            "analysis_timestamp": datetime.now().isoformat(),
            "manga_directory": manga_dir,
            "manga_title": aggregated_scores.get("manga_title", "Unknown"),
            "aggregated_scores": aggregated_scores,
            "bias_analysis": bias_analysis,
            "calibrated_scores": calibrated_scores,
            "calibration_report": calibration_report,
            "vqa_analyses": vqa_analyses,
            "final_summary": self._generate_final_summary(
                aggregated_scores, calibration_report, vqa_analyses
            )
        }
        
        return comprehensive_analysis
    
    def _generate_final_summary(self, aggregated_scores: Dict[str, Any], 
                              calibration_report: Dict[str, Any], 
                              vqa_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final summary with recommendations."""
        
        summary = aggregated_scores.get("summary", {})
        flagged_count = len(calibration_report.get("flagged_panels", []))
        vqa_count = len(vqa_analyses)
        
        # Calculate confidence level
        bias_recommendations = len(calibration_report.get("recommendations", []))
        confidence_level = "high"
        
        if bias_recommendations > 3 or flagged_count > 5:
            confidence_level = "low"
        elif bias_recommendations > 1 or flagged_count > 2:
            confidence_level = "medium"
        
        # Generate final recommendations
        final_recommendations = []
        
        if summary.get("success_rate", 0) < 70:
            final_recommendations.append("Overall quality below acceptable threshold - recommend regeneration")
        
        if flagged_count > 3:
            final_recommendations.append(f"{flagged_count} panels flagged for human review")
        
        if bias_recommendations > 0:
            final_recommendations.append("Systematic bias detected - review scoring methodology")
        
        if vqa_count > 0:
            vqa_issues = sum(1 for analysis in vqa_analyses.values() 
                           if analysis.get("recommend_human_review", False))
            if vqa_issues > 0:
                final_recommendations.append(f"{vqa_issues} panels require human visual verification")
        
        return {
            "overall_grade": summary.get("overall_grade", "Unknown"),
            "confidence_level": confidence_level,
            "success_rate": summary.get("success_rate", 0.0),
            "panels_flagged": flagged_count,
            "panels_analyzed_by_vqa": vqa_count,
            "bias_issues_detected": bias_recommendations,
            "final_recommendations": final_recommendations,
            "ready_for_publication": (
                summary.get("success_rate", 0) >= 80 and 
                confidence_level in ["high", "medium"] and 
                flagged_count <= 2
            )
        }
    
    def generate_markdown_report(self, analysis: Dict[str, Any], output_path: str):
        """Generate markdown report."""
        
        title = analysis.get("manga_title", "Unknown Manga")
        timestamp = analysis.get("analysis_timestamp", "Unknown")
        final_summary = analysis.get("final_summary", {})
        
        md_content = f"""# Final Evaluation Report: {title}

**Analysis Date:** {timestamp}
**Overall Grade:** {final_summary.get('overall_grade', 'Unknown')} {analysis.get('aggregated_scores', {}).get('summary', {}).get('grade_emoji', '')}
**Confidence Level:** {final_summary.get('confidence_level', 'Unknown').title()}
**Ready for Publication:** {'✅ Yes' if final_summary.get('ready_for_publication', False) else '❌ No'}

---

## Executive Summary

| Metric | Value |
|--------|-------|
| Success Rate | {final_summary.get('success_rate', 0.0):.1f}% |
| Panels Flagged | {final_summary.get('panels_flagged', 0)} |
| VQA Analyzed | {final_summary.get('panels_analyzed_by_vqa', 0)} |
| Bias Issues | {final_summary.get('bias_issues_detected', 0)} |

## Quality Distribution

"""
        
        # Add quality distribution
        quality_dist = analysis.get("aggregated_scores", {}).get("summary", {}).get("quality_distribution", {})
        md_content += f"""
| Quality Level | Count |
|---------------|-------|
| 🌟 Excellent | {quality_dist.get('excellent', 0)} |
| ✅ Good | {quality_dist.get('good', 0)} |
| ⚠️ Needs Fixing | {quality_dist.get('needs_fixing', 0)} |

"""
        
        # Add recommendations
        recommendations = final_summary.get("final_recommendations", [])
        if recommendations:
            md_content += "## Recommendations\n\n"
            for i, rec in enumerate(recommendations, 1):
                md_content += f"{i}. {rec}\n"
            md_content += "\n"
        
        # Add flagged panels
        flagged_panels = analysis.get("calibration_report", {}).get("flagged_panels", [])
        if flagged_panels:
            md_content += "## Flagged Panels\n\n"
            for panel in flagged_panels[:10]:  # Limit to top 10
                panel_id = panel.get("panel_id", "Unknown")
                flags = ", ".join(panel.get("flags", []))
                score = panel.get("composite_score", 0.0)
                md_content += f"- **{panel_id}** (Score: {score:.3f}) - {flags}\n"
            md_content += "\n"
        
        # Add VQA insights
        vqa_analyses = analysis.get("vqa_analyses", {})
        if vqa_analyses:
            md_content += "## VQA Analysis Results\n\n"
            for panel_id, vqa_result in vqa_analyses.items():
                if not vqa_result.get("error"):
                    emotion = vqa_result.get("emotion_detected", "unknown")
                    confidence = vqa_result.get("emotion_confidence", 0.0)
                    assessment = vqa_result.get("overall_assessment", "No assessment")
                    md_content += f"### {panel_id}\n"
                    md_content += f"- **Emotion Detected:** {emotion} (confidence: {confidence:.2f})\n"
                    md_content += f"- **Assessment:** {assessment}\n\n"
        
        # Save markdown report
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        print(f"📄 Markdown report saved: {output_path}")
    
    def generate_html_report(self, analysis: Dict[str, Any], output_path: str):
        """Generate HTML report with styling."""
        
        title = analysis.get("manga_title", "Unknown Manga")
        final_summary = analysis.get("final_summary", {})
        grade_emoji = analysis.get('aggregated_scores', {}).get('summary', {}).get('grade_emoji', '❓')
        
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Evaluation Report: {title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }}
        .grade {{ font-size: 3em; margin: 20px 0; }}
        .metric-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .metric-label {{ color: #666; margin-top: 5px; }}
        .section {{ margin: 30px 0; }}
        .section h2 {{ color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }}
        .flagged-panel {{ background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #ffc107; }}
        .vqa-result {{ background: #e7f3ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }}
        .recommendation {{ background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .danger {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Final Evaluation Report</h1>
            <h2>{title}</h2>
            <div class="grade">{grade_emoji} {final_summary.get('overall_grade', 'Unknown')}</div>
            <p><strong>Confidence:</strong> {final_summary.get('confidence_level', 'Unknown').title()}</p>
            <p><strong>Ready for Publication:</strong> 
                <span class="{'success' if final_summary.get('ready_for_publication', False) else 'danger'}">
                    {'✅ Yes' if final_summary.get('ready_for_publication', False) else '❌ No'}
                </span>
            </p>
        </div>
        
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">{final_summary.get('success_rate', 0.0):.1f}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{final_summary.get('panels_flagged', 0)}</div>
                <div class="metric-label">Panels Flagged</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{final_summary.get('panels_analyzed_by_vqa', 0)}</div>
                <div class="metric-label">VQA Analyzed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{final_summary.get('bias_issues_detected', 0)}</div>
                <div class="metric-label">Bias Issues</div>
            </div>
        </div>
"""
        
        # Add recommendations
        recommendations = final_summary.get("final_recommendations", [])
        if recommendations:
            html_content += """
        <div class="section">
            <h2>🎯 Recommendations</h2>
"""
            for rec in recommendations:
                html_content += f'            <div class="recommendation">{rec}</div>\n'
            html_content += "        </div>\n"
        
        # Add flagged panels
        flagged_panels = analysis.get("calibration_report", {}).get("flagged_panels", [])
        if flagged_panels:
            html_content += """
        <div class="section">
            <h2>⚠️ Flagged Panels</h2>
"""
            for panel in flagged_panels[:10]:
                panel_id = panel.get("panel_id", "Unknown")
                flags = ", ".join(panel.get("flags", []))
                score = panel.get("composite_score", 0.0)
                html_content += f"""
            <div class="flagged-panel">
                <strong>{panel_id}</strong> (Score: {score:.3f})<br>
                <em>Issues: {flags}</em>
            </div>
"""
            html_content += "        </div>\n"
        
        html_content += """
    </div>
</body>
</html>"""
        
        # Save HTML report
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"🌐 HTML report saved: {output_path}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Generate comprehensive final evaluation report",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/generate_final_report.py --input outputs/quality_manga_20250601_200706
    python scripts/generate_final_report.py --input outputs/fixed_manga_TIMESTAMP --output custom_report
        """
    )
    
    parser.add_argument(
        "--input",
        required=True,
        help="Path to manga output directory"
    )
    
    parser.add_argument(
        "--output",
        help="Output directory for reports (default: same as input)"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize report generator
        generator = FinalReportGenerator()
        
        # Generate comprehensive analysis
        analysis = generator.generate_comprehensive_analysis(args.input)
        
        # Determine output directory
        output_dir = Path(args.output) if args.output else Path(args.input)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate reports
        md_path = output_dir / "final_grade_report.md"
        html_path = output_dir / "final_grade_report.html"
        json_path = output_dir / "score_matrix.json"
        
        generator.generate_markdown_report(analysis, str(md_path))
        generator.generate_html_report(analysis, str(html_path))
        
        # Save complete analysis as JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2)
        
        print(f"\n🎉 Final evaluation complete!")
        print(f"📊 Reports generated in: {output_dir}")
        print(f"   📄 Markdown: {md_path.name}")
        print(f"   🌐 HTML: {html_path.name}")
        print(f"   📋 JSON: {json_path.name}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
