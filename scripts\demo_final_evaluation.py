#!/usr/bin/env python3
"""
Demo Final Evaluation Pipeline

Demonstrates the complete final evaluation system with score aggregation,
bias calibration, VQA analysis, and comprehensive reporting.
"""

import os
import sys
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def demo_evaluation_pipeline(manga_prompt: str = None):
    """
    Demonstrate the complete evaluation pipeline.
    
    Args:
        manga_prompt: Optional prompt for generating new manga to evaluate
    """
    
    print("🎯 Final Evaluation Pipeline Demo")
    print("=" * 50)
    
    # Step 1: Generate manga if prompt provided
    if manga_prompt:
        print(f"\n📚 Step 1: Generating manga from prompt...")
        print(f"   Prompt: {manga_prompt}")
        
        from scripts.generate_quality_manga import generate_quality_manga
        
        try:
            manga_dir, pdf_path = generate_quality_manga()
            print(f"   ✅ Manga generated: {manga_dir}")
        except Exception as e:
            print(f"   ❌ Manga generation failed: {e}")
            return False
    else:
        # Use existing manga
        outputs_dir = Path("outputs")
        manga_dirs = [d for d in outputs_dir.iterdir() if d.is_dir() and d.name.startswith("quality_manga_")]
        
        if not manga_dirs:
            print("❌ No existing manga found. Please provide a prompt to generate new manga.")
            return False
        
        manga_dir = str(sorted(manga_dirs)[-1])  # Use most recent
        print(f"\n📁 Using existing manga: {manga_dir}")
    
    # Step 2: Run score aggregation
    print(f"\n📊 Step 2: Score Aggregation...")
    
    from scripts.final_evaluator import ScoreAggregator
    
    try:
        aggregator = ScoreAggregator()
        aggregated_scores = aggregator.aggregate_scores(manga_dir)
        
        summary = aggregated_scores.get("summary", {})
        print(f"   ✅ Aggregation complete!")
        print(f"   📈 Overall Grade: {summary.get('grade_emoji', '❓')} {summary.get('overall_grade', 'Unknown')}")
        print(f"   🎯 Success Rate: {summary.get('success_rate', 0.0):.1f}%")
        
    except Exception as e:
        print(f"   ❌ Score aggregation failed: {e}")
        return False
    
    # Step 3: Bias calibration
    print(f"\n🎛️  Step 3: Bias Calibration...")
    
    from scripts.bias_calibrator import BiasCalibrator
    
    try:
        calibrator = BiasCalibrator()
        panel_scores = aggregated_scores.get("technical_scores", {}).get("panels", {})
        
        bias_analysis = calibrator.detect_systematic_bias(panel_scores)
        calibrated_scores = calibrator.calibrate_scores(panel_scores, bias_analysis)
        
        recommendations = bias_analysis.get("recommendations", [])
        print(f"   ✅ Bias calibration complete!")
        print(f"   🔍 Bias issues detected: {len(recommendations)}")
        
        if recommendations:
            for rec in recommendations[:3]:  # Show first 3
                print(f"      - {rec}")
        
    except Exception as e:
        print(f"   ❌ Bias calibration failed: {e}")
        return False
    
    # Step 4: VQA Analysis (limited demo)
    print(f"\n🧠 Step 4: Visual QA Analysis...")
    
    from scripts.visual_qa_agent import VisualQAAgent
    
    try:
        vqa_agent = VisualQAAgent()
        
        # Create mock flagged panels for demo (since images might be cleaned up)
        flagged_panels = []
        for panel_id, panel_data in list(panel_scores.items())[:2]:  # Demo with first 2 panels
            flagged_panels.append({
                "panel_id": panel_id,
                "flags": ["demo_flag"],
                "composite_score": panel_data.get("composite_score", 0.0),
                "emotion": panel_data.get("emotion", "neutral"),
                "description": panel_data.get("scene_description", ""),
                "panel_path": panel_data.get("panel_path", "")
            })
        
        print(f"   🔍 Analyzing {len(flagged_panels)} panels...")
        
        # Note: VQA might fail if images are cleaned up, but that's expected
        vqa_analyses = vqa_agent.batch_analyze_panels(flagged_panels)
        
        successful_analyses = sum(1 for analysis in vqa_analyses.values() 
                                if not analysis.get("error"))
        
        print(f"   ✅ VQA analysis complete!")
        print(f"   📊 Successful analyses: {successful_analyses}/{len(flagged_panels)}")
        
    except Exception as e:
        print(f"   ⚠️  VQA analysis had issues: {e}")
        vqa_analyses = {}
    
    # Step 5: Generate comprehensive report
    print(f"\n📄 Step 5: Report Generation...")
    
    from scripts.generate_final_report import FinalReportGenerator
    
    try:
        generator = FinalReportGenerator()
        
        # Generate comprehensive analysis
        analysis = generator.generate_comprehensive_analysis(manga_dir)
        
        # Generate reports
        output_dir = Path(manga_dir)
        md_path = output_dir / "final_grade_report.md"
        html_path = output_dir / "final_grade_report.html"
        json_path = output_dir / "score_matrix.json"
        
        generator.generate_markdown_report(analysis, str(md_path))
        generator.generate_html_report(analysis, str(html_path))
        
        print(f"   ✅ Reports generated!")
        print(f"   📄 Markdown: {md_path}")
        print(f"   🌐 HTML: {html_path}")
        print(f"   📋 JSON: {json_path}")
        
        # Show final summary
        final_summary = analysis.get("final_summary", {})
        print(f"\n🎉 Final Evaluation Results:")
        print(f"   📊 Overall Grade: {final_summary.get('overall_grade', 'Unknown')}")
        print(f"   🎯 Confidence: {final_summary.get('confidence_level', 'Unknown').title()}")
        print(f"   📈 Success Rate: {final_summary.get('success_rate', 0.0):.1f}%")
        print(f"   📝 Ready for Publication: {'✅ Yes' if final_summary.get('ready_for_publication', False) else '❌ No'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Report generation failed: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Demo the complete final evaluation pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/demo_final_evaluation.py
    python scripts/demo_final_evaluation.py --prompt "A robot learns to paint"
        """
    )
    
    parser.add_argument(
        "--prompt",
        help="Generate new manga with this prompt before evaluation"
    )
    
    args = parser.parse_args()
    
    try:
        success = demo_evaluation_pipeline(args.prompt)
        
        if success:
            print(f"\n✅ Demo completed successfully!")
            print(f"🎯 The final evaluation pipeline is working correctly.")
            return 0
        else:
            print(f"\n❌ Demo failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user.")
        return 1
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
