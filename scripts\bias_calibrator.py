#!/usr/bin/env python3
"""
Bias Calibration System

Detects and corrects for VLM scoring inconsistencies and model bias.
"""

import json
import statistics
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter


class BiasCalibrator:
    """Calibrates scores to account for VLM bias and inconsistencies."""
    
    def __init__(self):
        self.outlier_threshold = 2.0  # Z-score threshold for outliers
        self.bias_adjustment_factor = 0.1  # How much to adjust for bias
        
    def detect_outliers(self, scores: List[float]) -> Tuple[List[int], Dict[str, float]]:
        """
        Detect outlier scores using Z-score method.
        
        Returns:
            Tuple of (outlier_indices, statistics)
        """
        if len(scores) < 3:
            return [], {"mean": 0.0, "std": 0.0, "outlier_count": 0}
        
        mean_score = statistics.mean(scores)
        std_score = statistics.stdev(scores)
        
        outliers = []
        for i, score in enumerate(scores):
            if std_score > 0:
                z_score = abs(score - mean_score) / std_score
                if z_score > self.outlier_threshold:
                    outliers.append(i)
        
        return outliers, {
            "mean": mean_score,
            "std": std_score,
            "outlier_count": len(outliers),
            "outlier_percentage": len(outliers) / len(scores) * 100
        }
    
    def detect_systematic_bias(self, panel_scores: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect systematic bias patterns in scoring.
        
        Args:
            panel_scores: Dictionary of panel scores by panel_id
            
        Returns:
            Dictionary with bias analysis
        """
        bias_analysis = {
            "emotion_bias": {},
            "technical_bias": {},
            "issue_frequency": {},
            "recommendations": []
        }
        
        # Group scores by emotion
        emotion_scores = defaultdict(list)
        technical_scores = []
        issue_counts = Counter()
        
        for panel_id, panel_data in panel_scores.items():
            emotion = panel_data.get("emotion", "neutral")
            composite_score = panel_data.get("composite_score", 0.0)
            technical_score = panel_data.get("technical_score", 0.0)
            
            emotion_scores[emotion].append(composite_score)
            technical_scores.append(technical_score)
            
            # Count technical issues (simulated based on low scores)
            if technical_score < 0.5:
                issue_counts["low_technical_quality"] += 1
            if panel_data.get("generation_score", 0.0) < 1.0:
                issue_counts["generation_failure"] += 1
            if panel_data.get("dialogue_score", 0.0) < 1.0:
                issue_counts["dialogue_failure"] += 1
        
        # Analyze emotion bias
        overall_mean = statistics.mean([score for scores in emotion_scores.values() for score in scores])
        
        for emotion, scores in emotion_scores.items():
            if len(scores) >= 2:
                emotion_mean = statistics.mean(scores)
                bias_analysis["emotion_bias"][emotion] = {
                    "mean_score": emotion_mean,
                    "sample_count": len(scores),
                    "bias_vs_overall": emotion_mean - overall_mean,
                    "potentially_biased": abs(emotion_mean - overall_mean) > 0.2
                }
        
        # Analyze technical bias
        if technical_scores:
            tech_outliers, tech_stats = self.detect_outliers(technical_scores)
            bias_analysis["technical_bias"] = {
                "outlier_indices": tech_outliers,
                "statistics": tech_stats,
                "high_variance": tech_stats["std"] > 0.3
            }
        
        # Issue frequency analysis
        total_panels = len(panel_scores)
        for issue, count in issue_counts.items():
            frequency = count / total_panels if total_panels > 0 else 0.0
            bias_analysis["issue_frequency"][issue] = {
                "count": count,
                "frequency": frequency,
                "severity_adjustment": max(0.5, 1.0 - frequency) if frequency > 0.5 else 1.0
            }
        
        # Generate recommendations
        recommendations = []
        
        # Check for emotion bias
        for emotion, data in bias_analysis["emotion_bias"].items():
            if data["potentially_biased"]:
                if data["bias_vs_overall"] < -0.2:
                    recommendations.append(f"VLM may be overly harsh on '{emotion}' emotions - consider manual review")
                elif data["bias_vs_overall"] > 0.2:
                    recommendations.append(f"VLM may be overly lenient on '{emotion}' emotions - verify quality")
        
        # Check for high technical variance
        if bias_analysis["technical_bias"].get("high_variance", False):
            recommendations.append("High variance in technical scores detected - VLM may be inconsistent")
        
        # Check for common issues
        for issue, data in bias_analysis["issue_frequency"].items():
            if data["frequency"] > 0.7:
                recommendations.append(f"High frequency of '{issue}' ({data['frequency']:.1%}) - may indicate systematic problem")
        
        bias_analysis["recommendations"] = recommendations
        
        return bias_analysis
    
    def calibrate_scores(self, panel_scores: Dict[str, Any], bias_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply bias calibration to panel scores.
        
        Args:
            panel_scores: Original panel scores
            bias_analysis: Bias analysis results
            
        Returns:
            Calibrated panel scores
        """
        calibrated_scores = {}
        
        for panel_id, panel_data in panel_scores.items():
            calibrated_panel = panel_data.copy()
            
            # Apply emotion bias correction
            emotion = panel_data.get("emotion", "neutral")
            emotion_bias_data = bias_analysis.get("emotion_bias", {}).get(emotion, {})
            
            if emotion_bias_data.get("potentially_biased", False):
                bias_adjustment = emotion_bias_data.get("bias_vs_overall", 0.0)
                # Apply counter-bias (reduce the bias effect)
                adjustment_factor = -bias_adjustment * self.bias_adjustment_factor
                
                calibrated_panel["composite_score"] = max(0.0, min(1.0, 
                    panel_data.get("composite_score", 0.0) + adjustment_factor))
                calibrated_panel["bias_adjusted"] = True
                calibrated_panel["bias_adjustment"] = adjustment_factor
            else:
                calibrated_panel["bias_adjusted"] = False
                calibrated_panel["bias_adjustment"] = 0.0
            
            # Apply issue frequency adjustments
            for issue, issue_data in bias_analysis.get("issue_frequency", {}).items():
                severity_adjustment = issue_data.get("severity_adjustment", 1.0)
                if severity_adjustment < 1.0:
                    # If issue is very common, reduce its impact on scoring
                    if issue == "low_technical_quality" and panel_data.get("technical_score", 0.0) < 0.5:
                        calibrated_panel["technical_score"] = min(1.0, 
                            panel_data.get("technical_score", 0.0) * (1.0 + (1.0 - severity_adjustment)))
            
            # Recalculate composite score if any adjustments were made
            if calibrated_panel.get("bias_adjusted", False):
                calibrated_panel["composite_score"] = (
                    calibrated_panel.get("generation_score", 0.0) +
                    calibrated_panel.get("dialogue_score", 0.0) +
                    calibrated_panel.get("technical_score", 0.0)
                ) / 3.0
            
            calibrated_scores[panel_id] = calibrated_panel
        
        return calibrated_scores
    
    def flag_ambiguous_panels(self, panel_scores: Dict[str, Any], bias_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Flag panels that may need human review due to ambiguity or uncertainty.
        
        Returns:
            List of flagged panels with reasons
        """
        flagged_panels = []
        
        for panel_id, panel_data in panel_scores.items():
            flags = []
            
            # Flag outlier scores
            composite_score = panel_data.get("composite_score", 0.0)
            if composite_score < 0.3 or composite_score > 0.95:
                flags.append("extreme_score")
            
            # Flag panels with biased emotions
            emotion = panel_data.get("emotion", "neutral")
            emotion_bias_data = bias_analysis.get("emotion_bias", {}).get(emotion, {})
            if emotion_bias_data.get("potentially_biased", False):
                flags.append("emotion_bias_detected")
            
            # Flag technical inconsistencies
            tech_score = panel_data.get("technical_score", 0.0)
            gen_score = panel_data.get("generation_score", 0.0)
            if abs(tech_score - gen_score) > 0.5:
                flags.append("technical_generation_mismatch")
            
            # Flag panels with multiple issues
            issue_count = 0
            if panel_data.get("generation_score", 0.0) < 1.0:
                issue_count += 1
            if panel_data.get("dialogue_score", 0.0) < 1.0:
                issue_count += 1
            if panel_data.get("technical_score", 0.0) < 0.5:
                issue_count += 1
            
            if issue_count >= 2:
                flags.append("multiple_issues")
            
            if flags:
                flagged_panels.append({
                    "panel_id": panel_id,
                    "flags": flags,
                    "composite_score": composite_score,
                    "emotion": emotion,
                    "description": panel_data.get("scene_description", ""),
                    "panel_path": panel_data.get("panel_path", ""),
                    "recommend_human_review": len(flags) >= 2 or "extreme_score" in flags
                })
        
        return flagged_panels
    
    def generate_calibration_report(self, original_scores: Dict[str, Any], 
                                  calibrated_scores: Dict[str, Any], 
                                  bias_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a comprehensive calibration report."""
        
        # Calculate score changes
        score_changes = []
        for panel_id in original_scores:
            original = original_scores[panel_id].get("composite_score", 0.0)
            calibrated = calibrated_scores[panel_id].get("composite_score", 0.0)
            score_changes.append(calibrated - original)
        
        report = {
            "calibration_summary": {
                "panels_adjusted": sum(1 for panel in calibrated_scores.values() if panel.get("bias_adjusted", False)),
                "average_adjustment": statistics.mean(score_changes) if score_changes else 0.0,
                "max_positive_adjustment": max(score_changes) if score_changes else 0.0,
                "max_negative_adjustment": min(score_changes) if score_changes else 0.0
            },
            "bias_analysis": bias_analysis,
            "flagged_panels": self.flag_ambiguous_panels(calibrated_scores, bias_analysis),
            "recommendations": bias_analysis.get("recommendations", [])
        }
        
        return report
