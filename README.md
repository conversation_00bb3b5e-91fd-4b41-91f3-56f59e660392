# MangaGen – AI-Powered Manga Generation Framework

**MangaGen** is a clean, modular pipeline for generating manga-style content with AI. It combines ComfyUI for image generation, emotion extraction, and visual coherence validation.

## 🏗️ Project Structure

```
MangaGen/
├── core/                          # Core pipeline modules
│   ├── emotion_extractor.py       # Phase 7: Emotion extraction
│   ├── local_flow_checker.py      # Local OpenCV visual analysis
│   ├── coherence_analyzer.py      # Coherence analysis framework
│   └── validation/                # Phase 13 validation framework
│       ├── phase13_validator.py   # Real validation system
│       └── validation_runner.py   # Complete validation pipeline
├── scripts/                       # Entry points and CLI tools
│   ├── generate_panel.py          # ComfyUI panel generation
│   ├── run_validation.py          # Validation entry point
│   └── compile_pdf.py             # PDF compilation utility
├── pipeline/                      # Core manga generation pipeline
├── llm/                          # LLM integration modules
├── image_gen/                    # Image generation modules
├── assets/                       # Configuration and templates
│   ├── workflows/
│   │   └── manga_graph.json      # ComfyUI workflow
│   ├── prompts/
│   │   ├── base_prompts.txt       # Standard prompts
│   │   └── enhanced_prompts.txt   # Layout-enhanced prompts
│   └── configs/                   # Future configuration files
├── outputs/                      # Generated content
│   ├── panels/
│   │   ├── base/                 # Base panels
│   │   └── enhanced/             # Enhanced panels
│   ├── emotions/                 # Emotion extraction results
│   └── reports/                  # Validation reports
├── tests/                        # Future test files
├── requirements.txt
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Python 3.10+
- [ComfyUI](https://github.com/comfyanonymous/ComfyUI) running at `http://127.0.0.1:8188`
- OpenRouter API key (optional, for LLM features)

### Installation

```bash
git clone <your-repo-url>
cd MangaGen
pip install -r requirements.txt
```

### Basic Usage

#### Generate a Single Panel
```bash
python scripts/generate_panel.py "masterpiece, best quality manga style girl in city" outputs/my_panel.png
```

#### Generate Multiple Panels
```bash
python scripts/generate_panel.py --prompt-file assets/prompts/base_prompts.txt --output-dir outputs/panels/base
```

#### Run Validation Pipeline
```bash
python scripts/run_validation.py
```

#### Compile Panels to PDF
```bash
python scripts/compile_pdf.py outputs/panels/base --output my_manga.pdf
```

## 🧩 Core Components

### Panel Generation (`scripts/generate_panel.py`)
- Integrates with local ComfyUI instance
- Supports single and batch generation
- Uses workflow from `assets/workflows/manga_graph.json`

### Emotion Extraction (`core/emotion_extractor.py`)
- Analyzes dialogue for emotional content
- Keyword-based sentiment analysis
- Outputs structured emotion data

### Visual Flow Checker (`core/local_flow_checker.py`)
- OpenCV-based visual consistency analysis
- Scene transition validation
- Local processing (no external APIs)

### Coherence Analyzer (`core/coherence_analyzer.py`)
- Comprehensive narrative coherence analysis
- Integrates visual and textual analysis
- Supports both local and VLM-based analysis

### Validation Framework (`core/validation/`)
- Real panel validation system
- Compares base vs enhanced prompts
- Generates detailed validation reports

## 📊 Validation System

The validation system compares base prompts vs layout-enhanced prompts:

1. **Generate Base Panels**: Standard prompts without layout enhancements
2. **Generate Enhanced Panels**: Prompts with layout memory and composition rules
3. **Analyze Both Sets**: Visual flow, coherence, and consistency analysis
4. **Compare Results**: Determine if enhancements improve quality
5. **Generate Reports**: Detailed analysis and recommendations

### Validation Criteria
- **Coherence Score > 0.820**: Enhanced panels show improved narrative flow
- **Lighting Consistency ≥ 80%**: Better visual consistency across panels
- **Local Flow Coverage**: 100% local analysis without external API dependency

## 🔧 Configuration

### ComfyUI Workflow
Edit `assets/workflows/manga_graph.json` to customize:
- Model selection
- Image dimensions
- Sampling parameters
- Output settings

### Prompt Templates
- `assets/prompts/base_prompts.txt`: Standard generation prompts
- `assets/prompts/enhanced_prompts.txt`: Layout-enhanced prompts with memory

## 📈 Current Status

### ✅ Completed Features
- **Phase 7**: Emotion extraction from dialogue
- **ComfyUI Integration**: Real panel generation
- **Local Flow Checker**: OpenCV-based visual analysis
- **Validation Framework**: Real panel comparison system
- **PDF Compilation**: Professional manga layout

### 🔄 Recent Improvements
- Clean modular architecture
- Removed experimental/obsolete code
- Organized asset and output directories
- Fixed import paths and dependencies
- Comprehensive validation pipeline

## 🧪 Testing

### Test Panel Generation
```bash
python scripts/generate_panel.py "test prompt" outputs/test.png
```

### Test Validation Pipeline
```bash
python scripts/run_validation.py
```

### Verify Structure
```bash
# Check that all imports work
python -c "from core import EmotionExtractor, LocalFlowChecker, CoherenceAnalyzer; print('✅ Core imports working')"
```

## 🛠️ Development

### Adding New Features
1. Core functionality goes in `core/`
2. CLI tools go in `scripts/`
3. Configuration in `assets/`
4. Generated content in `outputs/`

### Code Organization
- **Modular Design**: Clear separation of concerns
- **Clean Imports**: No circular dependencies
- **Asset Management**: Centralized configuration
- **Output Organization**: Structured result storage

## 📝 License

This project is experimental and under active development.

---

*Last updated: 2025-06-02*
