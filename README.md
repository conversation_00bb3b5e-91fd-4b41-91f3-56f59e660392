# MangaGen - AI Manga Generation Pipeline

A complete pipeline for generating manga panels with AI, featuring organized output management, emotion extraction, and validation systems.

## 🚀 Quick Start

### Single Command Pipeline
```bash
# Generate from inline prompt
python scripts/run_full_pipeline.py --prompt "ninja discovers ancient temple"

# Generate from prompt files (default)
python scripts/run_full_pipeline.py

# Generate with custom name
python scripts/run_full_pipeline.py --run-name "my_manga_story"
```

### View Results
```bash
# List recent runs
python scripts/run_full_pipeline.py --list-runs

# Browse outputs in: outputs/runs/run_YYYYMMDD_HHMMSS/
```

## 📁 Project Structure

```
MangaGen/
├── 🚀 scripts/
│   └── run_full_pipeline.py          # ⭐ MAIN ENTRY POINT
├── 🧠 core/                          # Core pipeline modules
│   ├── emotion_extractor.py          # Emotion analysis
│   ├── local_flow_checker.py         # Visual flow validation
│   ├── coherence_analyzer.py         # Coherence analysis
│   └── output_manager.py             # Output organization
├── 🎨 image_gen/                     # Image generation
├── 🤖 llm/                           # LLM integration
├── ⚙️  pipeline/                      # Pipeline automation
├── 📦 assets/                        # Prompts and workflows
├── ⚙️  config/                       # Configuration
├── 🧪 tests/                         # Test cases (protected)
├── 📁 outputs/runs/                  # ⭐ ORGANIZED OUTPUT
└── 📦 archive/                       # Old outputs (hidden)
```

## 🎯 Features

### ✅ Complete Pipeline
- **Single Command**: Run entire pipeline with one script
- **Organized Output**: Clean folder structure per run
- **Auto Cleanup**: Configurable run retention
- **Progress Tracking**: Real-time status updates

### ✅ Panel Generation
- **Base Panels**: Standard manga style generation
- **Enhanced Panels**: Layout-enhanced with memory
- **Meaningful Names**: Descriptive filenames from prompts
- **ComfyUI Integration**: Local generation with workflow

### ✅ Analysis & Validation
- **Emotion Extraction**: Dialogue emotion analysis
- **Coherence Validation**: Panel consistency checking
- **Quality Metrics**: Automated scoring
- **Detailed Reports**: Human-readable results

### ✅ Output Management
- **Versioned Runs**: No overwrites, auto-increment
- **Clean Structure**: Everything organized per run
- **Easy Browsing**: Find results quickly
- **Configurable**: Customize retention and naming

## ⚙️ Configuration

Edit `config/output_config.json`:

```json
{
  "max_saved_runs": 10,
  "panel_naming_style": "descriptive",
  "auto_cleanup": true
}
```

## 🧪 Testing

```bash
# Run all tests
python tests/test_pipeline.py
```

## 📋 Usage Examples

### Basic Generation
```bash
# Simple prompt
python scripts/run_full_pipeline.py --prompt "samurai in bamboo forest"

# Custom run name
python scripts/run_full_pipeline.py --prompt "ninja adventure" --run-name "episode_01"
```

### Advanced Usage
```bash
# Custom prompt files
python scripts/run_full_pipeline.py \
  --base-prompts my_base.txt \
  --enhanced-prompts my_enhanced.txt \
  --run-name "custom_story"

# List and cleanup
python scripts/run_full_pipeline.py --list-runs
python scripts/run_full_pipeline.py --cleanup
```

### Output Structure
Each run creates:
```
outputs/runs/run_YYYYMMDD_HHMMSS/
├── panels/
│   ├── base/panel_001_scene_description.png
│   └── enhanced/panel_001_scene_description.png
├── validation/scores/validation_scores_TIMESTAMP.json
├── emotions/emotion_labels.json
└── run_summary.json
```

## 🔧 Requirements

- **Python 3.10+**
- **ComfyUI** running at `http://127.0.0.1:8188`
- **Dependencies**: `pip install -r requirements.txt`

## 🎯 Success Criteria Met

- ✅ **Single Entry Point**: `run_full_pipeline.py` handles everything
- ✅ **Clean Output**: One folder per run with organized structure
- ✅ **No Clutter**: Auto-cleanup prevents accumulation
- ✅ **Easy Browsing**: Clear naming and organization
- ✅ **Protected Tests**: Test folder safe from cleanup
- ✅ **Archived History**: Old outputs moved to archive

## 🚀 Ready to Use

The pipeline is now a stable, professional tool ready for manga generation!

---

*Generated by MangaGen Pipeline v1.0*
