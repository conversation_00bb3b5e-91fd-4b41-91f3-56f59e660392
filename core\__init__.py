"""
Core Module

Core manga generation components including emotion extraction,
visual flow checking, and coherence analysis.
"""

from .emotion_extractor import EmotionExtractor
from .local_flow_checker import LocalFlowChecker
from .coherence_analyzer import CoherenceAnalyzer
from .output_manager import OutputManager

__all__ = [
    "EmotionExtractor",
    "LocalFlowChecker",
    "CoherenceAnalyzer",
    "OutputManager"
]
