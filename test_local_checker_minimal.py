#!/usr/bin/env python3
"""
Minimal Test Script for Local Flow Checker
Isolates and tests LocalFlowChecker independently to identify import issues.
"""

import sys
import traceback
from pathlib import Path

def test_basic_imports():
    """Test basic Python imports first."""
    print("🔍 Testing Basic Imports")
    print("=" * 40)
    
    try:
        import cv2
        print(f"   ✅ OpenCV version: {cv2.__version__}")
    except ImportError as e:
        print(f"   ❌ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"   ✅ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"   ❌ NumPy import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print(f"   ✅ PIL available")
    except ImportError as e:
        print(f"   ⚠️  PIL not available: {e}")
    
    try:
        import imagehash
        print(f"   ✅ imagehash available")
    except ImportError as e:
        print(f"   ⚠️  imagehash not available: {e}")
    
    try:
        from skimage.metrics import structural_similarity
        print(f"   ✅ scikit-image SSIM available")
    except ImportError as e:
        print(f"   ⚠️  scikit-image not available: {e}")
    
    return True

def test_local_flow_checker_import():
    """Test LocalFlowChecker import specifically."""
    print("\n🔍 Testing LocalFlowChecker Import")
    print("=" * 40)
    
    try:
        # Add project root to path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        print("   📂 Project root added to path")
        
        # Try importing the module
        print("   📦 Importing local_flow_checker module...")
        from scripts.local_flow_checker import LocalFlowChecker
        print("   ✅ LocalFlowChecker imported successfully")
        
        # Try initializing
        print("   🔧 Initializing LocalFlowChecker...")
        checker = LocalFlowChecker()
        print("   ✅ LocalFlowChecker initialized successfully")
        
        return checker
        
    except Exception as e:
        print(f"   ❌ LocalFlowChecker import/init failed: {e}")
        print("   📋 Full traceback:")
        traceback.print_exc()
        return None

def test_basic_functionality(checker):
    """Test basic LocalFlowChecker functionality."""
    print("\n🔍 Testing Basic Functionality")
    print("=" * 40)
    
    if not checker:
        print("   ❌ No checker available for testing")
        return False
    
    try:
        # Test basic methods
        print("   🧪 Testing confidence threshold...")
        print(f"   📊 Confidence threshold: {checker.confidence_threshold}")
        
        print("   🧪 Testing similarity threshold...")
        print(f"   📊 Similarity threshold: {checker.similarity_threshold}")
        
        # Test with dummy data
        print("   🧪 Testing with dummy numpy arrays...")
        import numpy as np
        dummy_img1 = np.zeros((100, 100, 3), dtype=np.uint8)
        dummy_img2 = np.ones((100, 100, 3), dtype=np.uint8) * 255
        
        similarity = checker.calculate_structural_similarity(dummy_img1, dummy_img2)
        print(f"   📊 Dummy similarity score: {similarity:.3f}")
        
        print("   ✅ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_with_real_images():
    """Test with real images if available."""
    print("\n🔍 Testing with Real Images")
    print("=" * 40)
    
    # Look for existing panel images
    image_dirs = [
        "outputs/manga_panels",
        "outputs/panels",
        "outputs/generated_panels",
        "outputs/layout_enhanced_panels"
    ]
    
    test_images = []
    for img_dir in image_dirs:
        img_path = Path(img_dir)
        if img_path.exists():
            images = list(img_path.glob("*.png"))
            if len(images) >= 2:
                test_images = images[:2]
                print(f"   📁 Found test images in {img_dir}")
                break
    
    if not test_images:
        print("   ⚠️  No test images found - skipping real image test")
        return True
    
    try:
        from scripts.local_flow_checker import LocalFlowChecker
        checker = LocalFlowChecker()
        
        print(f"   🖼️  Testing with: {test_images[0].name} and {test_images[1].name}")
        
        # Test pair analysis
        result = checker.analyze_panel_pair(str(test_images[0]), str(test_images[1]))
        
        print(f"   📊 Analysis success: {result.get('success', False)}")
        print(f"   📊 Confidence: {result.get('confidence', 0.0):.3f}")
        print(f"   📊 Flow quality: {result.get('flow_quality', 'unknown')}")
        
        print("   ✅ Real image test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Real image test failed: {e}")
        traceback.print_exc()
        return False

def test_coherence_analyzer_integration():
    """Test CoherenceAnalyzer integration."""
    print("\n🔍 Testing CoherenceAnalyzer Integration")
    print("=" * 40)
    
    try:
        # Add project root to path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        print("   📦 Importing CoherenceAnalyzer...")
        from scripts.coherence_analyzer import CoherenceAnalyzer
        print("   ✅ CoherenceAnalyzer imported successfully")
        
        print("   🔧 Initializing with local flow checker...")
        analyzer = CoherenceAnalyzer(use_local_flow_checker=True)
        print("   ✅ CoherenceAnalyzer initialized successfully")
        
        print(f"   📊 Local flow checker enabled: {analyzer.use_local_flow_checker}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CoherenceAnalyzer integration failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all minimal tests."""
    print("Phase 13: Local Flow Checker Minimal Test")
    print("=" * 50)
    
    # Test 1: Basic imports
    basic_imports_ok = test_basic_imports()
    
    # Test 2: LocalFlowChecker import and init
    checker = test_local_flow_checker_import()
    
    # Test 3: Basic functionality
    basic_func_ok = test_basic_functionality(checker) if checker else False
    
    # Test 4: Real images (if available)
    real_images_ok = test_with_real_images() if checker else False
    
    # Test 5: CoherenceAnalyzer integration
    integration_ok = test_coherence_analyzer_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Basic Imports: {'✅ PASS' if basic_imports_ok else '❌ FAIL'}")
    print(f"   LocalFlowChecker: {'✅ PASS' if checker else '❌ FAIL'}")
    print(f"   Basic Functionality: {'✅ PASS' if basic_func_ok else '❌ FAIL'}")
    print(f"   Real Images: {'✅ PASS' if real_images_ok else '⚠️ SKIP'}")
    print(f"   Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    if basic_imports_ok and checker and basic_func_ok and integration_ok:
        print("\n🎉 All critical tests passed! LocalFlowChecker is working.")
        return 0
    else:
        print("\n❌ Some tests failed. Issues need to be resolved.")
        return 1

if __name__ == "__main__":
    exit(main())
