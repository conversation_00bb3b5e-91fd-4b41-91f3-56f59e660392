#!/usr/bin/env python3
"""
Phase 13 Integration Test
Tests all components of the Enhanced Prompt Testing + Local Visual Flow Validator
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all Phase 13 imports."""
    print("🧪 Testing Phase 13 Imports")
    print("=" * 50)
    
    try:
        print("   📦 Testing regeneration script...")
        from scripts.regenerate_enhanced_manga import EnhancedMangaRegenerator
        print("   ✅ EnhancedMangaRegenerator imported")
        
        print("   📦 Testing local flow checker...")
        from scripts.local_flow_checker import LocalFlowChecker
        print("   ✅ LocalFlowChecker imported")
        
        print("   📦 Testing coherence analyzer...")
        from scripts.coherence_analyzer import CoherenceAnalyzer
        print("   ✅ CoherenceAnalyzer imported")
        
        print("   📦 Testing comparison runner...")
        from scripts.run_coherence_comparison import CoherenceComparisonRunner
        print("   ✅ CoherenceComparisonRunner imported")
        
        print("   📦 Testing report generator...")
        from scripts.generate_comparison_report import ComparisonReportGenerator
        print("   ✅ ComparisonReportGenerator imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of Phase 13 components."""
    print("\n🔧 Testing Basic Functionality")
    print("=" * 50)
    
    try:
        # Test regeneration script
        print("   🎨 Testing regeneration script...")
        from scripts.regenerate_enhanced_manga import EnhancedMangaRegenerator
        regenerator = EnhancedMangaRegenerator()
        prompts = regenerator.load_enhanced_prompts()
        print(f"   ✅ Found {len(prompts)} enhanced prompts")
        
        # Test local flow checker (basic init)
        print("   🔍 Testing local flow checker...")
        from scripts.local_flow_checker import LocalFlowChecker
        checker = LocalFlowChecker()
        print("   ✅ Local flow checker initialized")
        
        # Test coherence analyzer
        print("   📊 Testing coherence analyzer...")
        from scripts.coherence_analyzer import CoherenceAnalyzer
        analyzer = CoherenceAnalyzer(use_local_flow_checker=True)
        print("   ✅ CoherenceAnalyzer initialized")
        
        # Test comparison runner
        print("   📋 Testing comparison runner...")
        from scripts.run_coherence_comparison import CoherenceComparisonRunner
        runner = CoherenceComparisonRunner()
        panel_sets = runner.find_panel_sets()
        print(f"   ✅ Found {len(panel_sets)} panel sets")
        
        # Test report generator
        print("   📄 Testing report generator...")
        from scripts.generate_comparison_report import ComparisonReportGenerator
        generator = ComparisonReportGenerator()
        print("   ✅ Report generator initialized")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase13_workflow():
    """Test the complete Phase 13 workflow."""
    print("\n🚀 Testing Phase 13 Workflow")
    print("=" * 50)
    
    try:
        # Check for enhanced prompts
        enhanced_prompts_dir = Path("outputs/enhanced_prompts_phase12")
        if not enhanced_prompts_dir.exists():
            print("   ⚠️  No enhanced prompts found - Phase 12 needs to be run first")
            return False
        
        prompt_files = list(enhanced_prompts_dir.glob("*.json"))
        print(f"   📂 Found {len(prompt_files)} enhanced prompt files")
        
        # Check for existing panels to compare
        panel_dirs = [
            "outputs/manga_panels",
            "outputs/panels", 
            "outputs/generated_panels",
            "outputs/layout_enhanced_panels"
        ]
        
        existing_panels = []
        for panel_dir in panel_dirs:
            panel_path = Path(panel_dir)
            if panel_path.exists():
                panels = list(panel_path.glob("*.png"))
                if panels:
                    existing_panels.append((panel_dir, len(panels)))
        
        print(f"   📊 Found panel sets: {existing_panels}")
        
        if len(existing_panels) >= 2:
            print("   ✅ Ready for comparison analysis")
        elif len(existing_panels) == 1:
            print("   ⚠️  Only one panel set found - need to regenerate with enhanced prompts")
        else:
            print("   ⚠️  No panel sets found - need to generate panels first")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Workflow test failed: {e}")
        return False

def main():
    """Run all Phase 13 tests."""
    print("Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator")
    print("Integration Test Suite")
    print("=" * 70)
    
    # Run tests
    test1_passed = test_imports()
    test2_passed = test_basic_functionality()
    test3_passed = test_phase13_workflow()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Phase 13 Test Summary:")
    print(f"   Imports: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Basic Functionality: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"   Workflow Ready: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 Phase 13 is ready for execution!")
        print("\n📋 Next Steps:")
        print("   1. Run: python scripts/regenerate_enhanced_manga.py")
        print("   2. Run: python scripts/run_coherence_comparison.py")
        print("   3. Run: python scripts/generate_comparison_report.py")
    else:
        print("\n⚠️  Phase 13 has issues that need to be resolved.")
        
        if not test1_passed:
            print("   🔧 Fix import issues first")
        if not test2_passed:
            print("   🔧 Fix functionality issues")
        if not test3_passed:
            print("   🔧 Ensure Phase 12 is completed and panels exist")

if __name__ == "__main__":
    main()
