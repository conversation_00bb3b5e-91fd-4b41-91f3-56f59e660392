#!/usr/bin/env python3
"""
Comparison Report Generator
Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator

This script generates detailed comparison reports in Markdown and HTML formats.
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ComparisonReportGenerator:
    """Generates detailed comparison reports for Phase 13 analysis."""
    
    def __init__(self, comparison_dir: str = "outputs/coherence_comparison_phase13"):
        """Initialize the report generator."""
        self.comparison_dir = Path(comparison_dir)
        self.output_dir = self.comparison_dir
        
    def load_comparison_data(self) -> Dict[str, Any]:
        """Load comparison data from JSON files."""
        
        print("📂 Loading comparison data...")
        
        files = {
            "original": self.comparison_dir / "original_scores.json",
            "enhanced": self.comparison_dir / "enhanced_scores.json", 
            "comparison": self.comparison_dir / "comparison_results.json"
        }
        
        data = {}
        
        for key, file_path in files.items():
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data[key] = json.load(f)
                    print(f"   ✅ Loaded {key} data from {file_path}")
                except Exception as e:
                    print(f"   ❌ Failed to load {key} data: {e}")
                    data[key] = {}
            else:
                print(f"   ⚠️  {key} data file not found: {file_path}")
                data[key] = {}
        
        return data
    
    def generate_markdown_report(self, data: Dict[str, Any]) -> str:
        """Generate detailed Markdown report."""
        
        original = data.get("original", {})
        enhanced = data.get("enhanced", {})
        comparison = data.get("comparison", {})
        
        # Extract key metrics
        scores = comparison.get("scores", {})
        methods = comparison.get("analysis_methods", {})
        success_criteria = comparison.get("success_criteria", {})
        
        report = f"""# Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator
## Coherence Analysis Comparison Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

---

## Executive Summary

### Key Results
- **Original Coherence Score:** {scores.get('original', 0.0):.3f}
- **Enhanced Coherence Score:** {scores.get('enhanced', 0.0):.3f}
- **Improvement Delta:** {scores.get('delta', 0.0):+.3f}
- **Percent Improvement:** {scores.get('percent_improvement', 0.0):+.1f}%

### Success Criteria Assessment
- ✅ **Coherence Improved:** {success_criteria.get('coherence_improved', False)}
- ✅ **Local Flow Coverage:** {methods.get('local_flow_checker_coverage', False)}
- ✅ **Meets 70% Threshold:** {success_criteria.get('meets_70_percent_threshold', False)}

### Overall Category: **{comparison.get('improvement_category', 'unknown').replace('_', ' ').title()}**

---

## Detailed Analysis

### Original Panel Analysis
- **Panel Count:** {original.get('panel_count', 0)}
- **Coherence Score:** {original.get('coherence_score', 0.0):.3f}
- **Overall Assessment:** {original.get('overall_assessment', 'unknown').title()}
- **Analysis Method:** {original.get('detailed_analysis', {}).get('visual_analysis', {}).get('analysis_method', 'unknown')}

#### Visual Consistency (Original)
"""
        
        # Add visual consistency details for original
        orig_visual = original.get("visual_consistency", {})
        for key, value in orig_visual.items():
            status = "✅" if value else "❌"
            report += f"- {status} **{key.replace('_', ' ').title()}:** {value}\n"
        
        report += f"""
#### Issues Detected (Original)
"""
        orig_issues = original.get("issues_detected", [])
        if orig_issues:
            for issue in orig_issues:
                report += f"- ⚠️ {issue}\n"
        else:
            report += "- ✅ No major issues detected\n"
        
        report += f"""
### Enhanced Panel Analysis
- **Panel Count:** {enhanced.get('panel_count', 0)}
- **Coherence Score:** {enhanced.get('coherence_score', 0.0):.3f}
- **Overall Assessment:** {enhanced.get('overall_assessment', 'unknown').title()}
- **Analysis Method:** {enhanced.get('detailed_analysis', {}).get('visual_analysis', {}).get('analysis_method', 'unknown')}

#### Visual Consistency (Enhanced)
"""
        
        # Add visual consistency details for enhanced
        enh_visual = enhanced.get("visual_consistency", {})
        for key, value in enh_visual.items():
            status = "✅" if value else "❌"
            report += f"- {status} **{key.replace('_', ' ').title()}:** {value}\n"
        
        report += f"""
#### Issues Detected (Enhanced)
"""
        enh_issues = enhanced.get("issues_detected", [])
        if enh_issues:
            for issue in enh_issues:
                report += f"- ⚠️ {issue}\n"
        else:
            report += "- ✅ No major issues detected\n"
        
        report += f"""
---

## Comparison Analysis

### Score Progression
```
Original:  {scores.get('original', 0.0):.3f} ({original.get('overall_assessment', 'unknown')})
Enhanced:  {scores.get('enhanced', 0.0):.3f} ({enhanced.get('overall_assessment', 'unknown')})
Delta:     {scores.get('delta', 0.0):+.3f} ({scores.get('percent_improvement', 0.0):+.1f}%)
```

### Analysis Method Comparison
- **Original Method:** {methods.get('original', 'unknown')}
- **Enhanced Method:** {methods.get('enhanced', 'unknown')}
- **Local Flow Checker Used:** {methods.get('local_flow_checker_coverage', False)}

### Improvement Categories

#### Visual Consistency Improvements
"""
        
        # Compare visual consistency
        for key in orig_visual.keys():
            orig_val = orig_visual.get(key, False)
            enh_val = enh_visual.get(key, False)
            
            if enh_val and not orig_val:
                report += f"- ✅ **{key.replace('_', ' ').title()}:** Improved (False → True)\n"
            elif not enh_val and orig_val:
                report += f"- ❌ **{key.replace('_', ' ').title()}:** Declined (True → False)\n"
            elif enh_val and orig_val:
                report += f"- ➡️ **{key.replace('_', ' ').title()}:** Maintained (True)\n"
            else:
                report += f"- ➡️ **{key.replace('_', ' ').title()}:** No change (False)\n"
        
        report += f"""
#### Issue Resolution
- **Original Issues:** {len(orig_issues)}
- **Enhanced Issues:** {len(enh_issues)}
- **Issues Resolved:** {max(0, len(orig_issues) - len(enh_issues))}
- **New Issues:** {max(0, len(enh_issues) - len(orig_issues))}

---

## Phase 13 Technology Assessment

### Local Flow Checker Performance
- **Coverage Rate:** {100 if methods.get('local_flow_checker_coverage', False) else 0}%
- **Fallback to VLM:** {'No' if methods.get('local_flow_checker_coverage', False) else 'Yes'}
- **Analysis Speed:** {'Fast (Local)' if methods.get('local_flow_checker_coverage', False) else 'Slower (VLM)'}

### Layout Enhancement Impact
- **Prompt Enhancement Applied:** Yes (Phase 12)
- **Layout Memory Used:** Yes
- **Composition Rules Applied:** Yes
- **Visual Coherence Impact:** {comparison.get('improvement_category', 'unknown').replace('_', ' ').title()}

---

## Conclusions and Recommendations

### Success Metrics
"""
        
        # Success assessment
        coherence_improved = success_criteria.get('coherence_improved', False)
        local_flow_used = methods.get('local_flow_checker_coverage', False)
        meets_threshold = success_criteria.get('meets_70_percent_threshold', False)
        
        if coherence_improved and local_flow_used and meets_threshold:
            report += "🎉 **PHASE 13 SUCCESS:** All success criteria met!\n\n"
        elif coherence_improved and meets_threshold:
            report += "✅ **PHASE 13 PARTIAL SUCCESS:** Coherence improved significantly\n\n"
        elif coherence_improved:
            report += "⚠️ **PHASE 13 MIXED RESULTS:** Some improvement but below threshold\n\n"
        else:
            report += "❌ **PHASE 13 NEEDS WORK:** No significant improvement detected\n\n"
        
        report += f"""### Key Findings
1. **Layout Enhancement Effectiveness:** {comparison.get('improvement_category', 'unknown').replace('_', ' ').title()}
2. **Local Flow Checker Coverage:** {100 if local_flow_used else 0}% of analysis
3. **Coherence Score Change:** {scores.get('delta', 0.0):+.3f} points
4. **Relative Improvement:** {scores.get('percent_improvement', 0.0):+.1f}%

### Recommendations
"""
        
        if coherence_improved:
            report += "- ✅ Continue using layout enhancement for manga generation\n"
            report += "- ✅ Phase 12 layout memory system is effective\n"
        else:
            report += "- ⚠️ Review layout enhancement parameters\n"
            report += "- ⚠️ Consider adjusting composition rules\n"
        
        if local_flow_used:
            report += "- ✅ Local flow checker is working effectively\n"
            report += "- ✅ Reduced dependency on VLM models achieved\n"
        else:
            report += "- ⚠️ Local flow checker needs improvement\n"
            report += "- ⚠️ Still relying on VLM fallback\n"
        
        report += f"""
---

## Technical Details

### Analysis Configuration
- **Local Flow Checker:** {'Enabled' if local_flow_used else 'Disabled/Failed'}
- **VLM Model:** {enhanced.get('detailed_analysis', {}).get('visual_analysis', {}).get('model_used', 'unknown')}
- **Analysis Timestamp:** {comparison.get('comparison_timestamp', 'unknown')}

### File Locations
- **Original Scores:** `outputs/coherence_comparison_phase13/original_scores.json`
- **Enhanced Scores:** `outputs/coherence_comparison_phase13/enhanced_scores.json`
- **Comparison Data:** `outputs/coherence_comparison_phase13/comparison_results.json`

---

*Report generated by Phase 13 Comparison Report Generator*
"""
        
        return report
    
    def generate_html_report(self, markdown_content: str) -> str:
        """Convert Markdown report to HTML."""
        
        # Simple Markdown to HTML conversion
        html_content = markdown_content
        
        # Convert headers
        html_content = html_content.replace("# ", "<h1>").replace("\n", "</h1>\n", 1)
        html_content = html_content.replace("## ", "<h2>").replace("\n", "</h2>\n")
        html_content = html_content.replace("### ", "<h3>").replace("\n", "</h3>\n")
        html_content = html_content.replace("#### ", "<h4>").replace("\n", "</h4>\n")
        
        # Convert bold text
        import re
        html_content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html_content)
        
        # Convert lists
        html_content = re.sub(r'^- (.*?)$', r'<li>\1</li>', html_content, flags=re.MULTILINE)
        html_content = re.sub(r'(<li>.*?</li>\n)+', r'<ul>\n\g<0></ul>\n', html_content, flags=re.DOTALL)
        
        # Convert code blocks
        html_content = re.sub(r'```\n(.*?)\n```', r'<pre><code>\1</code></pre>', html_content, flags=re.DOTALL)
        
        # Convert line breaks
        html_content = html_content.replace('\n', '<br>\n')
        
        # Wrap in HTML document
        html_document = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 13 Coherence Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }}
        h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; }}
        h3 {{ color: #7f8c8d; }}
        pre {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        ul {{ margin-left: 20px; }}
        li {{ margin-bottom: 5px; }}
        .success {{ color: #27ae60; }}
        .warning {{ color: #f39c12; }}
        .error {{ color: #e74c3c; }}
    </style>
</head>
<body>
{html_content}
</body>
</html>"""
        
        return html_document
    
    def generate_reports(self) -> Dict[str, str]:
        """Generate both Markdown and HTML reports."""
        
        print("📊 Generating comparison reports...")
        
        # Load data
        data = self.load_comparison_data()
        
        if not any(data.values()):
            print("❌ No comparison data found - cannot generate reports")
            return {}
        
        # Generate Markdown report
        print("   📝 Generating Markdown report...")
        markdown_content = self.generate_markdown_report(data)
        
        # Generate HTML report
        print("   🌐 Generating HTML report...")
        html_content = self.generate_html_report(markdown_content)
        
        # Save reports
        md_file = self.output_dir / "layout_vs_original.md"
        html_file = self.output_dir / "layout_vs_original.html"
        
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"   ✅ Markdown report: {md_file}")
        print(f"   ✅ HTML report: {html_file}")
        
        return {
            "markdown": str(md_file),
            "html": str(html_file)
        }


def main():
    """Main report generation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate Phase 13 Comparison Reports")
    parser.add_argument("--comparison-dir", type=str, 
                       default="outputs/coherence_comparison_phase13",
                       help="Directory containing comparison data")
    
    args = parser.parse_args()
    
    print("Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator")
    print("Task 5: Generate Comparison Reports")
    print("=" * 70)
    
    # Generate reports
    generator = ComparisonReportGenerator(args.comparison_dir)
    report_files = generator.generate_reports()
    
    if report_files:
        print(f"\n🎉 Reports generated successfully!")
        print(f"📄 View Markdown: {report_files.get('markdown', 'N/A')}")
        print(f"🌐 View HTML: {report_files.get('html', 'N/A')}")
        return 0
    else:
        print(f"\n❌ Report generation failed")
        return 1


if __name__ == "__main__":
    exit(main())
