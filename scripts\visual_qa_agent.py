#!/usr/bin/env python3
"""
Visual QA Agent

Uses free-tier VLM models to perform visual quality assessment
and provide human-readable explanations for manga panels.
"""

import os
import json
import base64
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import time


class VisualQAAgent:
    """Visual Quality Assessment using free VLM models."""
    
    def __init__(self):
        # Primary and fallback API keys
        self.api_keys = [
            "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",
            "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"
        ]
        self.current_key_index = 0
        
        # Free VLM models to try
        self.models = [
            "qwen/qwen2.5-vl-7b-instruct:free",
            "meta-llama/llama-3.2-11b-vision-instruct:free"
        ]
        self.current_model_index = 0
        
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        
    def encode_image(self, image_path: str) -> str:
        """Encode image to base64 for API."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"Error encoding image {image_path}: {e}")
            return ""
    
    def call_vlm_api(self, image_path: str, prompt: str, max_retries: int = 2) -> Optional[Dict[str, Any]]:
        """Call VLM API with fallback handling."""
        
        if not Path(image_path).exists():
            return {"error": "Image file not found", "success": False}
        
        # Encode image
        base64_image = self.encode_image(image_path)
        if not base64_image:
            return {"error": "Failed to encode image", "success": False}
        
        for attempt in range(max_retries):
            try:
                # Get current API key and model
                api_key = self.api_keys[self.current_key_index]
                model = self.models[self.current_model_index]
                
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": prompt
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    }
                                }
                            ]
                        }
                    ],
                    "max_tokens": 500,
                    "temperature": 0.1
                }
                
                response = requests.post(self.base_url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    return {"content": content, "success": True, "model": model}
                
                elif response.status_code == 429:
                    print(f"Rate limit hit for {model}, trying next key/model...")
                    self._switch_to_next_option()
                    time.sleep(2)
                    continue
                    
                else:
                    print(f"API error {response.status_code}: {response.text}")
                    self._switch_to_next_option()
                    continue
                    
            except Exception as e:
                print(f"VLM API call failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
        
        return {"error": "All API attempts failed", "success": False}
    
    def _switch_to_next_option(self):
        """Switch to next API key or model."""
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        if self.current_key_index == 0:  # Cycled through all keys
            self.current_model_index = (self.current_model_index + 1) % len(self.models)
    
    def analyze_panel_quality(self, image_path: str, scene_description: str, emotion: str) -> Dict[str, Any]:
        """
        Analyze a single panel for visual quality and scene alignment.
        
        Args:
            image_path: Path to the panel image
            scene_description: Expected scene description
            emotion: Expected emotion
            
        Returns:
            Analysis results with scores and explanations
        """
        
        prompt = f"""Analyze this manga panel image and provide a detailed assessment.

Expected Scene: {scene_description}
Expected Emotion: {emotion}

Please evaluate and respond in JSON format:
{{
  "scene_understood": true/false,
  "scene_alignment_score": 0.0-1.0,
  "scene_explanation": "brief explanation of what you see",
  "emotion_detected": "detected emotion",
  "emotion_confidence": 0.0-1.0,
  "emotion_matches_expected": true/false,
  "visual_quality": {{
    "clarity": 0.0-1.0,
    "composition": 0.0-1.0,
    "character_detail": 0.0-1.0
  }},
  "dialogue_bubbles": {{
    "present": true/false,
    "placement_quality": 0.0-1.0,
    "readability": 0.0-1.0
  }},
  "visual_ambiguity": "low/medium/high",
  "recommend_human_review": true/false,
  "overall_assessment": "brief overall assessment",
  "improvement_suggestions": ["suggestion1", "suggestion2"]
}}

Focus on accuracy and be honest about what you can and cannot clearly see."""
        
        # Call VLM API
        api_result = self.call_vlm_api(image_path, prompt)
        
        if not api_result.get("success", False):
            return {
                "error": api_result.get("error", "Unknown error"),
                "scene_understood": False,
                "emotion_detected": "unknown",
                "emotion_confidence": 0.0,
                "visual_ambiguity": "high",
                "recommend_human_review": True,
                "model_used": "none"
            }
        
        # Parse JSON response
        try:
            content = api_result.get("content", "")
            # Extract JSON from response (handle cases where model adds extra text)
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_content = content[json_start:json_end]
                analysis = json.loads(json_content)
                analysis["model_used"] = api_result.get("model", "unknown")
                analysis["raw_response"] = content
                return analysis
            else:
                # Fallback parsing
                return self._parse_fallback_response(content, api_result.get("model", "unknown"))
                
        except json.JSONDecodeError as e:
            print(f"Failed to parse VLM response as JSON: {e}")
            return self._parse_fallback_response(api_result.get("content", ""), api_result.get("model", "unknown"))
    
    def _parse_fallback_response(self, content: str, model: str) -> Dict[str, Any]:
        """Parse non-JSON response as fallback."""
        content_lower = content.lower()
        
        # Simple keyword-based parsing
        scene_understood = any(word in content_lower for word in ["yes", "clear", "visible", "depicts"])
        emotion_confidence = 0.5  # Default medium confidence
        
        # Try to extract emotion
        emotions = ["happy", "sad", "angry", "surprised", "fearful", "disgusted", "neutral", 
                   "determined", "mystical", "respectful", "confrontational", "intense", 
                   "awestruck", "triumphant", "joyful"]
        
        detected_emotion = "neutral"
        for emotion in emotions:
            if emotion in content_lower:
                detected_emotion = emotion
                break
        
        return {
            "scene_understood": scene_understood,
            "scene_alignment_score": 0.6 if scene_understood else 0.3,
            "scene_explanation": content[:100] + "..." if len(content) > 100 else content,
            "emotion_detected": detected_emotion,
            "emotion_confidence": emotion_confidence,
            "emotion_matches_expected": True,  # Default assumption
            "visual_quality": {
                "clarity": 0.5,
                "composition": 0.5,
                "character_detail": 0.5
            },
            "dialogue_bubbles": {
                "present": "bubble" in content_lower or "dialogue" in content_lower,
                "placement_quality": 0.5,
                "readability": 0.5
            },
            "visual_ambiguity": "medium",
            "recommend_human_review": not scene_understood,
            "overall_assessment": "Automated fallback analysis",
            "improvement_suggestions": ["Manual review recommended"],
            "model_used": model,
            "raw_response": content,
            "fallback_parsing": True
        }
    
    def batch_analyze_panels(self, flagged_panels: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze multiple flagged panels in batch.
        
        Args:
            flagged_panels: List of panels flagged for review
            
        Returns:
            Dictionary of panel analyses
        """
        analyses = {}
        
        print(f"🧠 Starting VQA analysis of {len(flagged_panels)} flagged panels...")
        
        for i, panel_info in enumerate(flagged_panels):
            panel_id = panel_info.get("panel_id", f"panel_{i}")
            image_path = panel_info.get("panel_path", "")
            description = panel_info.get("description", "")
            emotion = panel_info.get("emotion", "neutral")
            
            print(f"   Analyzing {panel_id}...")
            
            if not image_path or not Path(image_path).exists():
                analyses[panel_id] = {
                    "error": "Image path not found",
                    "recommend_human_review": True
                }
                continue
            
            # Analyze panel
            analysis = self.analyze_panel_quality(image_path, description, emotion)
            analyses[panel_id] = analysis
            
            # Small delay to avoid rate limiting
            time.sleep(1)
        
        print(f"✅ VQA analysis complete!")
        
        return analyses
