#!/usr/bin/env python3
"""
Extract Real Prompts for Phase 13 Validation
Extracts both enhanced and base prompts for real panel generation comparison.
"""

import json
import sys
from pathlib import Path

def extract_prompts():
    """Extract enhanced and base prompts from Phase 12 data."""
    
    print("📂 Extracting Real Prompts for Phase 13 Validation")
    print("=" * 60)
    
    enhanced_dir = Path("outputs/enhanced_prompts_phase12")
    if not enhanced_dir.exists():
        print("❌ Enhanced prompts directory not found!")
        return None, None
    
    # Get first 6 prompt files
    prompt_files = sorted(list(enhanced_dir.glob("*.json")))[:6]
    
    if len(prompt_files) < 6:
        print(f"❌ Only found {len(prompt_files)} prompt files, need 6!")
        return None, None
    
    enhanced_prompts = []
    base_prompts = []
    
    print(f"📊 Processing {len(prompt_files)} prompt files...")
    
    for i, prompt_file in enumerate(prompt_files):
        print(f"   📄 Processing {prompt_file.name}...")
        
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            enhanced_prompt = data.get('enhanced_prompt', '')
            base_prompt = data.get('base_prompt', '')
            scene_description = data.get('scene_description', '')
            
            if not enhanced_prompt or not base_prompt:
                print(f"   ⚠️  Missing prompts in {prompt_file.name}")
                continue
            
            enhanced_prompts.append({
                'panel_index': i,
                'enhanced_prompt': enhanced_prompt,
                'scene_description': scene_description,
                'source_file': prompt_file.name
            })
            
            base_prompts.append({
                'panel_index': i,
                'base_prompt': base_prompt,
                'scene_description': scene_description,
                'source_file': prompt_file.name
            })
            
            print(f"   ✅ Extracted prompts for panel {i}")
            
        except Exception as e:
            print(f"   ❌ Error processing {prompt_file.name}: {e}")
            continue
    
    print(f"\n📊 Extraction Summary:")
    print(f"   Enhanced prompts: {len(enhanced_prompts)}")
    print(f"   Base prompts: {len(base_prompts)}")
    
    return enhanced_prompts, base_prompts

def save_prompt_sets(enhanced_prompts, base_prompts):
    """Save the extracted prompt sets for reference."""
    
    output_dir = Path("outputs/phase13_real_validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save enhanced prompts
    enhanced_file = output_dir / "enhanced_prompts.json"
    with open(enhanced_file, 'w', encoding='utf-8') as f:
        json.dump(enhanced_prompts, f, indent=2)
    
    # Save base prompts
    base_file = output_dir / "base_prompts.json"
    with open(base_file, 'w', encoding='utf-8') as f:
        json.dump(base_prompts, f, indent=2)
    
    print(f"💾 Saved enhanced prompts to: {enhanced_file}")
    print(f"💾 Saved base prompts to: {base_file}")
    
    return enhanced_file, base_file

def display_prompt_comparison(enhanced_prompts, base_prompts):
    """Display the first few prompts for comparison."""
    
    print("\n📋 Prompt Comparison Sample:")
    print("=" * 60)
    
    for i in range(min(3, len(enhanced_prompts), len(base_prompts))):
        enhanced = enhanced_prompts[i]
        base = base_prompts[i]
        
        print(f"\n🎨 Panel {i} - {enhanced['scene_description']}")
        print("-" * 40)
        print(f"📝 Base Prompt:")
        print(f"   {base['base_prompt'][:100]}...")
        print(f"📝 Enhanced Prompt:")
        print(f"   {enhanced['enhanced_prompt'][:100]}...")
        
        # Check for layout enhancements
        enhanced_text = enhanced['enhanced_prompt'].lower()
        enhancements = []
        if 'layout_memory' in enhanced_text:
            enhancements.append('Layout Memory')
        if 'lighting' in enhanced_text:
            enhancements.append('Lighting Rules')
        if 'composition' in enhanced_text:
            enhancements.append('Composition Rules')
        if 'continuity' in enhanced_text:
            enhancements.append('Continuity')
        
        print(f"🔧 Enhancements: {', '.join(enhancements) if enhancements else 'None detected'}")

def main():
    """Main extraction function."""
    
    print("Phase 13: Real Prompt Extraction for Validation")
    print("NO SIMULATION - REAL PROMPTS ONLY")
    print("=" * 70)
    
    # Extract prompts
    enhanced_prompts, base_prompts = extract_prompts()
    
    if not enhanced_prompts or not base_prompts:
        print("❌ Failed to extract prompts!")
        return 1
    
    if len(enhanced_prompts) < 6 or len(base_prompts) < 6:
        print(f"❌ Insufficient prompts: Enhanced={len(enhanced_prompts)}, Base={len(base_prompts)}")
        return 1
    
    # Save prompt sets
    enhanced_file, base_file = save_prompt_sets(enhanced_prompts, base_prompts)
    
    # Display comparison
    display_prompt_comparison(enhanced_prompts, base_prompts)
    
    print(f"\n✅ Real prompt extraction complete!")
    print(f"📁 Enhanced prompts: {enhanced_file}")
    print(f"📁 Base prompts: {base_file}")
    print(f"\n🎯 Next Steps:")
    print(f"   1. Generate 6 panels using base prompts (original set)")
    print(f"   2. Generate 6 panels using enhanced prompts (enhanced set)")
    print(f"   3. Run real coherence comparison")
    print(f"   4. Report actual results (no simulation)")
    
    return 0

if __name__ == "__main__":
    exit(main())
