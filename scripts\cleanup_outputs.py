#!/usr/bin/env python3
"""
Output Cleanup Utility

Cleans up the outputs folder by removing intermediate files and keeping only essential manga outputs.
This helps reduce clutter and keeps only the final manga PDFs and metadata.

Usage:
    python scripts/cleanup_outputs.py
    python scripts/cleanup_outputs.py --dry-run  # Preview what will be deleted
    python scripts/cleanup_outputs.py --keep-validation  # Keep validation folders
"""

import os
import sys
import shutil
import argparse
from pathlib import Path
from datetime import datetime


def cleanup_outputs_folder(outputs_dir="outputs", dry_run=False, keep_validation=False):
    """
    Clean up the outputs folder, keeping only essential files.
    
    Args:
        outputs_dir: Path to outputs directory
        dry_run: If True, only show what would be deleted
        keep_validation: If True, keep validation folders
    """
    outputs_path = Path(outputs_dir)
    
    if not outputs_path.exists():
        print(f"❌ Outputs directory not found: {outputs_dir}")
        return
    
    print(f"🧹 {'[DRY RUN] ' if dry_run else ''}Cleaning up outputs folder: {outputs_path}")
    
    # Files and folders to keep in each manga directory
    keep_files = {
        'Final_Manga.pdf',
        'manga_results.json', 
        'story_structure.json'
    }
    
    if keep_validation:
        keep_files.add('validation')
    
    total_removed = 0
    total_size_saved = 0
    
    # Process each item in outputs folder
    for item in outputs_path.iterdir():
        if item.is_file():
            # Remove loose files in outputs root
            if item.name not in {'README.md', '.gitkeep'}:
                size = item.stat().st_size if item.exists() else 0
                total_size_saved += size
                total_removed += 1
                
                if dry_run:
                    print(f"   🗑️  [DRY RUN] Would remove file: {item.name}")
                else:
                    print(f"   🗑️  Removing file: {item.name}")
                    item.unlink()
        
        elif item.is_dir():
            # Process manga directories
            if item.name.startswith(('manga_', 'quality_manga_')):
                # This is a manga output directory - clean it up
                removed_count = cleanup_manga_directory(item, keep_files, dry_run)
                total_removed += removed_count
            
            elif item.name.startswith(('20250', 'panel_', 'quality_failures')):
                # These are intermediate/temporary directories - remove entirely
                if item.name == 'quality_failures' and not dry_run:
                    # Keep quality_failures but clean old files
                    for failure_file in item.iterdir():
                        if failure_file.is_file():
                            size = failure_file.stat().st_size
                            total_size_saved += size
                            total_removed += 1
                            print(f"   🗑️  Removing quality failure: {failure_file.name}")
                            failure_file.unlink()
                else:
                    # Remove entire directory
                    dir_size = get_directory_size(item)
                    total_size_saved += dir_size
                    total_removed += 1
                    
                    if dry_run:
                        print(f"   🗑️  [DRY RUN] Would remove directory: {item.name}")
                    else:
                        print(f"   🗑️  Removing directory: {item.name}")
                        shutil.rmtree(item)
    
    # Summary
    size_mb = total_size_saved / (1024 * 1024)
    print(f"\n✅ Cleanup {'preview' if dry_run else 'complete'}!")
    print(f"📊 {'Would remove' if dry_run else 'Removed'} {total_removed} items")
    print(f"💾 {'Would save' if dry_run else 'Saved'} {size_mb:.1f} MB of disk space")


def cleanup_manga_directory(manga_dir, keep_files, dry_run=False):
    """
    Clean up a specific manga directory, keeping only essential files.
    
    Args:
        manga_dir: Path to manga directory
        keep_files: Set of files/folders to keep
        dry_run: If True, only show what would be deleted
        
    Returns:
        Number of items removed
    """
    removed_count = 0
    
    print(f"   📁 Processing manga directory: {manga_dir.name}")
    
    for item in manga_dir.iterdir():
        if item.name not in keep_files:
            removed_count += 1
            
            if dry_run:
                print(f"      🗑️  [DRY RUN] Would remove: {item.name}")
            else:
                print(f"      🗑️  Removing: {item.name}")
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
    
    return removed_count


def get_directory_size(directory):
    """Get the total size of a directory in bytes."""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except (OSError, IOError):
        pass
    return total_size


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Clean up manga outputs folder",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/cleanup_outputs.py                    # Clean up outputs folder
    python scripts/cleanup_outputs.py --dry-run          # Preview cleanup
    python scripts/cleanup_outputs.py --keep-validation  # Keep validation folders
        """
    )
    
    parser.add_argument(
        "--dry-run", 
        action="store_true",
        help="Preview what would be deleted without actually deleting"
    )
    
    parser.add_argument(
        "--keep-validation",
        action="store_true", 
        help="Keep validation folders"
    )
    
    parser.add_argument(
        "--outputs-dir",
        default="outputs",
        help="Path to outputs directory (default: outputs)"
    )
    
    args = parser.parse_args()
    
    try:
        cleanup_outputs_folder(
            outputs_dir=args.outputs_dir,
            dry_run=args.dry_run,
            keep_validation=args.keep_validation
        )
        
    except KeyboardInterrupt:
        print("\n⚠️  Cleanup interrupted by user.")
        return 1
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
