#!/usr/bin/env python3
"""
Phase 7-13 Audit Script
Comprehensive validation of all prior phases to verify actual implementation.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class PhaseAuditor:
    """Audits Phases 7-13 for actual implementation and outputs."""
    
    def __init__(self):
        """Initialize the auditor."""
        self.audit_results = {}
        self.output_dir = Path("outputs/phase_audit_7_13")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def audit_phase_7_emotion_extraction(self):
        """Audit Phase 7: Emotion Extraction."""
        
        print("🔍 Auditing Phase 7: Emotion Extraction")
        print("-" * 50)
        
        # Check for emotion extraction implementation
        emotion_files = [
            "scripts/emotion_extractor.py",
            "scripts/emotion_analyzer.py",
            "scripts/dialogue_emotion_mapper.py"
        ]
        
        # Check for emotion outputs
        emotion_outputs = [
            "outputs/emotion_analysis",
            "outputs/dialogue_emotions",
            "outputs/character_emotions"
        ]
        
        implementation_found = any(Path(f).exists() for f in emotion_files)
        outputs_found = any(Path(d).exists() for d in emotion_outputs)
        
        # Check existing manga outputs for emotion data
        manga_dirs = list(Path("outputs").glob("*manga*"))
        emotion_in_manga = False
        
        for manga_dir in manga_dirs:
            if manga_dir.is_dir():
                # Check for emotion data in manga results
                results_file = manga_dir / "manga_results.json"
                if results_file.exists():
                    try:
                        with open(results_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        # Look for emotion-related data
                        if any('emotion' in str(data).lower() for _ in [1]):
                            emotion_in_manga = True
                            break
                    except:
                        pass
        
        trust_score = "High" if implementation_found and outputs_found else "Medium" if implementation_found else "Low"
        needs_retesting = not (implementation_found and (outputs_found or emotion_in_manga))
        
        result = {
            "phase": 7,
            "name": "Emotion Extraction",
            "intended_output": "Extract emotions from dialogue and map to characters",
            "actual_output": {
                "implementation_files": implementation_found,
                "emotion_outputs": outputs_found,
                "emotion_in_manga": emotion_in_manga,
                "files_found": [f for f in emotion_files if Path(f).exists()],
                "outputs_found": [d for d in emotion_outputs if Path(d).exists()]
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if not implementation_found:
            result["issues"].append("No emotion extraction implementation found")
        if not outputs_found and not emotion_in_manga:
            result["issues"].append("No emotion analysis outputs found")
        
        print(f"   Implementation: {'✅' if implementation_found else '❌'}")
        print(f"   Outputs: {'✅' if outputs_found or emotion_in_manga else '❌'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def audit_phase_8_visual_validator(self):
        """Audit Phase 8: Visual Validator."""
        
        print("\n🔍 Auditing Phase 8: Visual Validator")
        print("-" * 50)
        
        # Check for visual validation implementation
        visual_files = [
            "scripts/visual_validator.py",
            "scripts/image_quality_checker.py",
            "scripts/panel_validator.py"
        ]
        
        # Check for VLM integration
        vlm_integration = False
        opencv_integration = False
        
        # Check existing scripts for VLM/OpenCV usage
        script_files = list(Path("scripts").glob("*.py"))
        for script_file in script_files:
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                if 'vlm' in content.lower() or 'vision' in content.lower():
                    vlm_integration = True
                if 'cv2' in content or 'opencv' in content.lower():
                    opencv_integration = True
            except:
                pass
        
        implementation_found = any(Path(f).exists() for f in visual_files)
        
        # Check for validation outputs
        validation_outputs = list(Path("outputs").glob("*validation*")) + list(Path("outputs").glob("*quality*"))
        outputs_found = len(validation_outputs) > 0
        
        trust_score = "High" if implementation_found and vlm_integration and opencv_integration else "Medium" if vlm_integration or opencv_integration else "Low"
        needs_retesting = not (vlm_integration and opencv_integration)
        
        result = {
            "phase": 8,
            "name": "Visual Validator",
            "intended_output": "VLM and OpenCV-based visual validation",
            "actual_output": {
                "implementation_files": implementation_found,
                "vlm_integration": vlm_integration,
                "opencv_integration": opencv_integration,
                "validation_outputs": len(validation_outputs),
                "files_found": [f for f in visual_files if Path(f).exists()],
                "outputs_found": [str(p) for p in validation_outputs]
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if not vlm_integration:
            result["issues"].append("No VLM integration found")
        if not opencv_integration:
            result["issues"].append("No OpenCV integration found")
        if not outputs_found:
            result["issues"].append("No validation outputs found")
        
        print(f"   VLM Integration: {'✅' if vlm_integration else '❌'}")
        print(f"   OpenCV Integration: {'✅' if opencv_integration else '❌'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def audit_phase_9_10_dialogue_processing(self):
        """Audit Phase 9-10: Scene Parsing and Dialogue Tracking."""
        
        print("\n🔍 Auditing Phase 9-10: Scene Parsing & Dialogue Tracking")
        print("-" * 50)
        
        # Check for dialogue processing implementation
        dialogue_files = [
            "scripts/scene_parser.py",
            "scripts/dialogue_tracker.py",
            "scripts/bubble_placer.py",
            "scripts/dialogue_processor.py"
        ]
        
        # Check for bubble placement outputs
        bubble_outputs = []
        manga_dirs = list(Path("outputs").glob("*manga*"))
        
        for manga_dir in manga_dirs:
            if manga_dir.is_dir():
                bubble_files = list(manga_dir.rglob("*bubble*.png"))
                bubble_outputs.extend(bubble_files)
        
        implementation_found = any(Path(f).exists() for f in dialogue_files)
        bubble_placement_working = len(bubble_outputs) > 0
        
        # Check for dialogue threading in outputs
        dialogue_threading = False
        for manga_dir in manga_dirs:
            results_file = manga_dir / "manga_results.json"
            if results_file.exists():
                try:
                    with open(results_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if 'dialogue' in str(data).lower() or 'speaker' in str(data).lower():
                        dialogue_threading = True
                        break
                except:
                    pass
        
        trust_score = "High" if implementation_found and bubble_placement_working and dialogue_threading else "Medium" if bubble_placement_working else "Low"
        needs_retesting = not (bubble_placement_working and dialogue_threading)
        
        result = {
            "phase": "9-10",
            "name": "Scene Parsing & Dialogue Tracking",
            "intended_output": "Bubble placement and dialogue threading",
            "actual_output": {
                "implementation_files": implementation_found,
                "bubble_placement_working": bubble_placement_working,
                "dialogue_threading": dialogue_threading,
                "bubble_files_count": len(bubble_outputs),
                "files_found": [f for f in dialogue_files if Path(f).exists()],
                "bubble_samples": [str(p) for p in bubble_outputs[:3]]
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if not bubble_placement_working:
            result["issues"].append("No bubble placement outputs found")
        if not dialogue_threading:
            result["issues"].append("No dialogue threading evidence found")
        
        print(f"   Bubble Placement: {'✅' if bubble_placement_working else '❌'}")
        print(f"   Dialogue Threading: {'✅' if dialogue_threading else '❌'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def audit_phase_11_prompt_enhancer(self):
        """Audit Phase 11: Prompt Enhancer."""
        
        print("\n🔍 Auditing Phase 11: Prompt Enhancer")
        print("-" * 50)
        
        # Check for prompt enhancement implementation
        enhancer_files = [
            "scripts/prompt_enhancer.py",
            "scripts/layout_enhancer.py",
            "scripts/scene_enhancer.py"
        ]
        
        # Check for enhanced prompt outputs
        enhanced_prompt_dirs = [
            "outputs/enhanced_prompts",
            "outputs/improved_prompts",
            "outputs/layout_prompts"
        ]
        
        implementation_found = any(Path(f).exists() for f in enhancer_files)
        enhanced_outputs = any(Path(d).exists() for d in enhanced_prompt_dirs)
        
        # Check for layout rules in existing prompts
        layout_rules_found = False
        scene_cues_found = False
        
        for prompt_dir in enhanced_prompt_dirs:
            prompt_path = Path(prompt_dir)
            if prompt_path.exists():
                for prompt_file in prompt_path.glob("*.json"):
                    try:
                        with open(prompt_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        content = str(data).lower()
                        if 'layout' in content or 'composition' in content:
                            layout_rules_found = True
                        if 'scene' in content or 'setting' in content:
                            scene_cues_found = True
                    except:
                        pass
        
        trust_score = "High" if implementation_found and enhanced_outputs and layout_rules_found else "Medium" if enhanced_outputs else "Low"
        needs_retesting = not (enhanced_outputs and layout_rules_found)
        
        result = {
            "phase": 11,
            "name": "Prompt Enhancer",
            "intended_output": "Enhanced prompts with layout rules and scene cues",
            "actual_output": {
                "implementation_files": implementation_found,
                "enhanced_outputs": enhanced_outputs,
                "layout_rules_found": layout_rules_found,
                "scene_cues_found": scene_cues_found,
                "files_found": [f for f in enhancer_files if Path(f).exists()],
                "outputs_found": [d for d in enhanced_prompt_dirs if Path(d).exists()]
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if not layout_rules_found:
            result["issues"].append("No layout rules found in enhanced prompts")
        if not scene_cues_found:
            result["issues"].append("No scene cues found in enhanced prompts")
        
        print(f"   Enhanced Outputs: {'✅' if enhanced_outputs else '❌'}")
        print(f"   Layout Rules: {'✅' if layout_rules_found else '❌'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def audit_phase_12_layout_memory(self):
        """Audit Phase 12: Layout Memory."""
        
        print("\n🔍 Auditing Phase 12: Layout Memory")
        print("-" * 50)
        
        # Check for Phase 12 specific outputs
        phase12_dir = Path("outputs/enhanced_prompts_phase12")
        layout_memory_found = phase12_dir.exists()
        
        # Check for layout memory implementation
        memory_files = [
            "scripts/layout_memory.py",
            "scripts/composition_tracker.py",
            "scripts/scene_memory.py"
        ]
        
        implementation_found = any(Path(f).exists() for f in memory_files)
        
        # Check for layout memory in Phase 12 prompts
        layout_memory_in_prompts = False
        pose_consistency = False
        composition_evolution = False
        
        if phase12_dir.exists():
            for prompt_file in phase12_dir.glob("*.json"):
                try:
                    with open(prompt_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    enhanced_prompt = data.get('enhanced_prompt', '').lower()
                    if 'layout_memory' in enhanced_prompt:
                        layout_memory_in_prompts = True
                    if 'character_pos' in enhanced_prompt or 'pose' in enhanced_prompt:
                        pose_consistency = True
                    if 'composition' in enhanced_prompt or 'continuity' in enhanced_prompt:
                        composition_evolution = True
                except:
                    pass
        
        trust_score = "High" if layout_memory_found and layout_memory_in_prompts and pose_consistency else "Medium" if layout_memory_found else "Low"
        needs_retesting = not (layout_memory_found and layout_memory_in_prompts)
        
        result = {
            "phase": 12,
            "name": "Layout Memory",
            "intended_output": "Scene-to-scene pose and composition consistency",
            "actual_output": {
                "phase12_outputs": layout_memory_found,
                "implementation_files": implementation_found,
                "layout_memory_in_prompts": layout_memory_in_prompts,
                "pose_consistency": pose_consistency,
                "composition_evolution": composition_evolution,
                "files_found": [f for f in memory_files if Path(f).exists()],
                "prompt_count": len(list(phase12_dir.glob("*.json"))) if phase12_dir.exists() else 0
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if not layout_memory_in_prompts:
            result["issues"].append("No layout memory found in Phase 12 prompts")
        if not pose_consistency:
            result["issues"].append("No pose consistency tracking found")
        
        print(f"   Phase 12 Outputs: {'✅' if layout_memory_found else '❌'}")
        print(f"   Layout Memory: {'✅' if layout_memory_in_prompts else '❌'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def audit_phase_13_coherence_check(self):
        """Audit Phase 13: Coherence Check."""
        
        print("\n🔍 Auditing Phase 13: Coherence Check")
        print("-" * 50)
        
        # Check for Phase 13 implementation
        phase13_files = [
            "scripts/local_flow_checker.py",
            "scripts/coherence_analyzer.py",
            "scripts/run_coherence_comparison.py",
            "phase13_real_validation.py"
        ]
        
        implementation_found = sum(1 for f in phase13_files if Path(f).exists())
        
        # Check for real validation vs simulation
        real_validation_attempted = Path("phase13_real_validation.py").exists()
        simulation_detected = Path("simulate_enhanced_improvement.py").exists()
        
        # Check for coherence outputs
        coherence_outputs = list(Path("outputs").glob("*coherence*"))
        phase13_outputs = list(Path("outputs").glob("*phase13*"))
        
        # Check honest status report
        honest_status = Path("PHASE13_HONEST_STATUS.md").exists()
        
        # Determine if real panels were used
        real_panels_used = False
        if phase13_outputs:
            for output_dir in phase13_outputs:
                if output_dir.is_dir():
                    comparison_file = output_dir / "comparison_results.json"
                    if comparison_file.exists():
                        try:
                            with open(comparison_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            if data.get('validation_type') == 'real_panels':
                                real_panels_used = True
                        except:
                            pass
        
        trust_score = "High" if real_validation_attempted and honest_status and not simulation_detected else "Medium" if implementation_found >= 3 else "Low"
        needs_retesting = not real_panels_used or simulation_detected
        
        result = {
            "phase": 13,
            "name": "Coherence Check",
            "intended_output": "Real panel coherence validation (no simulation)",
            "actual_output": {
                "implementation_files": implementation_found,
                "real_validation_framework": real_validation_attempted,
                "simulation_detected": simulation_detected,
                "real_panels_used": real_panels_used,
                "honest_status_report": honest_status,
                "coherence_outputs": len(coherence_outputs),
                "files_found": [f for f in phase13_files if Path(f).exists()]
            },
            "trust_score": trust_score,
            "needs_retesting": needs_retesting,
            "issues": []
        }
        
        if simulation_detected:
            result["issues"].append("Simulation code detected - real validation required")
        if not real_panels_used:
            result["issues"].append("No evidence of real panel validation")
        if not honest_status:
            result["issues"].append("No honest status assessment found")
        
        print(f"   Real Validation: {'✅' if real_validation_attempted else '❌'}")
        print(f"   Real Panels Used: {'✅' if real_panels_used else '❌'}")
        print(f"   Simulation Detected: {'❌' if simulation_detected else '✅'}")
        print(f"   Trust Score: {trust_score}")
        
        return result
    
    def generate_audit_report(self, results):
        """Generate comprehensive audit report."""
        
        report = f"""# Phase 7-13 Audit Report
## Comprehensive Validation of Prior Phases

**Audit Date:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**Audit Type:** Non-destructive consistency check

---

## 📊 Executive Summary

"""
        
        # Count trust scores
        high_trust = sum(1 for r in results if r['trust_score'] == 'High')
        medium_trust = sum(1 for r in results if r['trust_score'] == 'Medium')
        low_trust = sum(1 for r in results if r['trust_score'] == 'Low')
        needs_retesting = sum(1 for r in results if r['needs_retesting'])
        
        report += f"""### Trust Score Distribution
- **High Trust:** {high_trust}/{len(results)} phases
- **Medium Trust:** {medium_trust}/{len(results)} phases  
- **Low Trust:** {low_trust}/{len(results)} phases
- **Needs Retesting:** {needs_retesting}/{len(results)} phases

### Overall Assessment
"""
        
        if needs_retesting > len(results) // 2:
            report += "🚨 **CRITICAL**: Multiple phases need retesting. **FREEZE PHASE 14**.\n\n"
        elif needs_retesting > 0:
            report += "⚠️ **WARNING**: Some phases need retesting before Phase 14.\n\n"
        else:
            report += "✅ **GOOD**: All phases show acceptable implementation.\n\n"
        
        report += "---\n\n## 📋 Detailed Phase Analysis\n\n"
        
        # Add detailed results for each phase
        for result in results:
            phase_num = result['phase']
            name = result['name']
            trust = result['trust_score']
            retest = result['needs_retesting']
            
            trust_emoji = "🟢" if trust == "High" else "🟡" if trust == "Medium" else "🔴"
            retest_emoji = "❌" if retest else "✅"
            
            report += f"""### Phase {phase_num}: {name}

| Metric | Value |
|--------|-------|
| **Intended Output** | {result['intended_output']} |
| **Trust Score** | {trust_emoji} {trust} |
| **Needs Retesting** | {retest_emoji} {'Yes' if retest else 'No'} |

#### Actual Implementation
"""
            
            actual = result['actual_output']
            for key, value in actual.items():
                if isinstance(value, bool):
                    emoji = "✅" if value else "❌"
                    report += f"- **{key.replace('_', ' ').title()}:** {emoji} {value}\n"
                elif isinstance(value, (int, float)):
                    report += f"- **{key.replace('_', ' ').title()}:** {value}\n"
                elif isinstance(value, list):
                    if value:
                        report += f"- **{key.replace('_', ' ').title()}:** {len(value)} items\n"
                        for item in value[:3]:  # Show first 3 items
                            report += f"  - {item}\n"
                        if len(value) > 3:
                            report += f"  - ... and {len(value) - 3} more\n"
                    else:
                        report += f"- **{key.replace('_', ' ').title()}:** None found\n"
            
            if result['issues']:
                report += "\n#### Issues Detected\n"
                for issue in result['issues']:
                    report += f"- ⚠️ {issue}\n"
            
            report += "\n---\n\n"
        
        # Add recommendations
        report += "## 🎯 Recommendations\n\n"
        
        critical_phases = [r for r in results if r['trust_score'] == 'Low' or r['needs_retesting']]
        
        if critical_phases:
            report += "### Critical Actions Required\n\n"
            for phase in critical_phases:
                report += f"#### Phase {phase['phase']}: {phase['name']}\n"
                if phase['issues']:
                    for issue in phase['issues']:
                        report += f"- 🔧 Fix: {issue}\n"
                report += f"- 🧪 Retest: Verify {phase['intended_output'].lower()}\n\n"
        
        report += "### Phase 14 Readiness\n\n"
        
        if needs_retesting > len(results) // 2:
            report += "🛑 **DO NOT PROCEED TO PHASE 14**\n\n"
            report += "Multiple critical phases need retesting. Address all issues before continuing.\n\n"
        elif needs_retesting > 0:
            report += "⚠️ **CONDITIONAL PROCEED TO PHASE 14**\n\n"
            report += "Some phases need attention, but core functionality appears intact.\n\n"
        else:
            report += "✅ **READY FOR PHASE 14**\n\n"
            report += "All phases show acceptable implementation and outputs.\n\n"
        
        report += "---\n\n"
        report += "*Audit completed by Phase Auditor - Non-destructive validation*"
        
        return report
    
    def run_full_audit(self):
        """Run complete audit of Phases 7-13."""
        
        print("🔍 Phase 7-13 Comprehensive Audit")
        print("=" * 70)
        print("Non-destructive validation of prior phase implementations")
        print("=" * 70)
        
        # Run individual phase audits
        results = []
        
        results.append(self.audit_phase_7_emotion_extraction())
        results.append(self.audit_phase_8_visual_validator())
        results.append(self.audit_phase_9_10_dialogue_processing())
        results.append(self.audit_phase_11_prompt_enhancer())
        results.append(self.audit_phase_12_layout_memory())
        results.append(self.audit_phase_13_coherence_check())
        
        # Generate report
        report = self.generate_audit_report(results)
        
        # Save report
        report_file = self.output_dir / "phase_audit_7_13_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save raw results
        results_file = self.output_dir / "audit_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 Audit Complete!")
        print(f"📄 Report: {report_file}")
        print(f"📊 Raw Data: {results_file}")
        
        # Summary
        needs_retesting = sum(1 for r in results if r['needs_retesting'])
        if needs_retesting > len(results) // 2:
            print(f"\n🚨 CRITICAL: {needs_retesting}/{len(results)} phases need retesting!")
            print("🛑 FREEZE PHASE 14 until issues resolved!")
            return 2
        elif needs_retesting > 0:
            print(f"\n⚠️ WARNING: {needs_retesting}/{len(results)} phases need attention")
            return 1
        else:
            print(f"\n✅ GOOD: All phases show acceptable implementation")
            return 0

def main():
    """Main audit function."""
    auditor = PhaseAuditor()
    return auditor.run_full_audit()

if __name__ == "__main__":
    exit(main())
