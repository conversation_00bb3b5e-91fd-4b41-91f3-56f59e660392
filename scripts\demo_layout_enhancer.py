#!/usr/bin/env python3
"""
Demo Script for Phase 12: Auto Layout & Panel Composition Engine

This script demonstrates the layout enhancer functionality and shows
before/after comparisons of prompt enhancement.
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from image_gen.prompt_builder import PromptBuilder, create_story_prompts
from scripts.panel_prompt_enhancer import enhance_manga_prompts


def demo_basic_enhancement():
    """Demonstrate basic prompt enhancement."""
    
    print("🎨 Demo 1: Basic Prompt Enhancement")
    print("=" * 50)
    
    # Sample prompts
    base_prompts = [
        "A young samurai warrior stands in a peaceful forest clearing",
        "The samurai draws his katana as enemies emerge from the shadows", 
        "Close-up of the samurai's determined face before the battle",
        "Dynamic action shot of the samurai fighting multiple opponents",
        "The samurai stands victorious among defeated enemies"
    ]
    
    print(f"📝 Enhancing {len(base_prompts)} prompts...")
    
    # Enhance with layout memory
    enhanced_results = enhance_manga_prompts(
        base_prompts, 
        sequence_id=f"demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    print("\n📊 Before vs After Comparison:")
    for i, (enhanced_prompt, metadata) in enumerate(enhanced_results):
        print(f"\nPanel {i+1}:")
        print(f"  📝 Original: {base_prompts[i]}")
        print(f"  ✨ Enhanced: {enhanced_prompt[:100]}...")
        print(f"  🎯 Layout: {metadata['camera_angle']} | {metadata['background']} | {metadata['lighting']}")
        print(f"  🔗 Continuity: {len(metadata.get('continuity_applied', []))} rules applied")


def demo_story_integration():
    """Demonstrate integration with story-based prompt generation."""
    
    print("\n🎬 Demo 2: Story Integration with Layout Enhancement")
    print("=" * 50)
    
    # Sample story data
    story_data = {
        "title": "The Crystal Sword",
        "scenes": [
            "A young ninja discovers an ancient temple hidden in the mountains",
            "Inside the temple, the ninja finds a mysterious glowing crystal sword",
            "Ancient guardians awaken to protect the sacred weapon",
            "The ninja must prove their worth through combat trials",
            "The ninja claims the crystal sword and gains its power",
            "Enemies attack the temple, seeking to steal the weapon",
            "The ninja uses the crystal sword's power to defend the temple",
            "The ninja escapes the temple with the sacred weapon",
            "The ninja returns to their village as a legendary warrior"
        ]
    }
    
    print(f"📚 Story: {story_data['title']}")
    print(f"📊 Scenes: {len(story_data['scenes'])}")
    
    # Generate prompts without layout enhancement
    print("\n🔄 Generating prompts WITHOUT layout enhancement...")
    basic_prompts = create_story_prompts(story_data, use_layout_enhancer=False)
    
    # Generate prompts with layout enhancement
    print("🎨 Generating prompts WITH layout enhancement...")
    enhanced_prompts = create_story_prompts(
        story_data, 
        use_layout_enhancer=True, 
        sequence_id="story_demo"
    )
    
    print("\n📋 Comparison Results:")
    for i in range(min(3, len(basic_prompts))):  # Show first 3 panels
        basic_prompt, _ = basic_prompts[i]
        enhanced_prompt, _ = enhanced_prompts[i]
        
        print(f"\nPanel {i+1}:")
        print(f"  📝 Basic: {basic_prompt[:80]}...")
        print(f"  ✨ Enhanced: {enhanced_prompt[:80]}...")
        print(f"  🔍 Has Layout Memory: {'LAYOUT_MEMORY' in enhanced_prompt}")


def demo_composition_rules():
    """Demonstrate composition rules in action."""
    
    print("\n🎯 Demo 3: Composition Rules in Action")
    print("=" * 50)
    
    # Scenario with location changes
    location_change_prompts = [
        "The hero stands in a peaceful village square",
        "The hero travels through a dark forest path",  # Location change
        "The hero arrives at a mysterious castle",      # Another location change
        "Inside the castle, the hero explores the throne room",
        "The hero confronts the villain in the castle dungeon"
    ]
    
    scene_descriptions = [
        "Establishing shot of village setting",
        "Transition scene showing travel",
        "New location establishing shot", 
        "Interior castle scene",
        "Dramatic confrontation scene"
    ]
    
    print("📍 Testing location change handling...")
    
    enhanced_results = enhance_manga_prompts(
        location_change_prompts,
        scene_descriptions,
        sequence_id="composition_demo"
    )
    
    print("\n🔍 Composition Rules Analysis:")
    for i, (enhanced_prompt, metadata) in enumerate(enhanced_results):
        continuity_rules = metadata.get('continuity_applied', [])
        print(f"\nPanel {i+1}:")
        print(f"  📍 Location: {metadata['background']}")
        print(f"  📷 Camera: {metadata['camera_angle']}")
        print(f"  🔗 Continuity Rules: {len(continuity_rules)}")
        for rule in continuity_rules:
            print(f"    • {rule}")


def demo_layout_memory():
    """Demonstrate layout memory persistence."""
    
    print("\n🧠 Demo 4: Layout Memory Persistence")
    print("=" * 50)
    
    # Create a prompt builder with layout enhancement
    builder = PromptBuilder(use_layout_enhancer=True)
    
    # Reset for new sequence
    builder.reset_layout_memory("memory_demo")
    
    # Generate several panels
    test_scenes = [
        "Hero in forest clearing",
        "Hero walking through forest",
        "Hero still in forest, looking around",
        "Hero finds a cave in the forest"
    ]
    
    print("🔄 Generating panels with memory tracking...")
    
    for i, scene in enumerate(test_scenes):
        story_data = {"scenes": [scene]}
        positive, negative = builder.build_manga_panel_prompt(story_data, 0, panel_index=i)
        
        print(f"\nPanel {i+1}: {scene}")
        print(f"  Enhanced: {'LAYOUT_MEMORY' in positive}")
        
        # Show layout summary
        summary = builder.get_layout_summary()
        if summary.get("total_panels", 0) > 0:
            current_seq = summary.get("current_sequence", {})
            print(f"  Memory: {current_seq.get('background', 'unknown')} | {current_seq.get('lighting', 'unknown')}")


def main():
    """Main demo function."""
    
    parser = argparse.ArgumentParser(description="Layout Enhancer Demo")
    parser.add_argument("--demo", type=int, choices=[1, 2, 3, 4], 
                       help="Run specific demo (1-4)")
    parser.add_argument("--all", action="store_true", 
                       help="Run all demos")
    
    args = parser.parse_args()
    
    print("Phase 12: Auto Layout & Panel Composition Engine")
    print("Demo Script")
    print("=" * 60)
    
    if args.demo == 1 or args.all:
        demo_basic_enhancement()
    
    if args.demo == 2 or args.all:
        demo_story_integration()
    
    if args.demo == 3 or args.all:
        demo_composition_rules()
    
    if args.demo == 4 or args.all:
        demo_layout_memory()
    
    if not args.demo and not args.all:
        print("\nAvailable Demos:")
        print("  --demo 1    Basic prompt enhancement")
        print("  --demo 2    Story integration")
        print("  --demo 3    Composition rules")
        print("  --demo 4    Layout memory")
        print("  --all       Run all demos")
        print("\nExample: python scripts/demo_layout_enhancer.py --demo 1")


if __name__ == "__main__":
    main()
