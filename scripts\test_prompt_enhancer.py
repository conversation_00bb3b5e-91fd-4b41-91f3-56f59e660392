#!/usr/bin/env python3
"""
Test Script for Panel Prompt Enhancer
Phase 12: Auto Layout & Panel Composition Engine

This script tests the layout memory system, composition rules engine,
and prompt enhancement functionality.
"""

import os
import sys
import json
import unittest
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.panel_prompt_enhancer import (
    LayoutMemory, 
    CompositionRulesEngine, 
    PanelPromptEnhancer,
    AutoMetadataTracker,
    enhance_manga_prompts
)


class TestLayoutMemory(unittest.TestCase):
    """Test the LayoutMemory class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_memory_file = "test_layout_memory.json"
        self.layout_memory = LayoutMemory(self.test_memory_file)
    
    def tearDown(self):
        """Clean up test files."""
        if Path(self.test_memory_file).exists():
            Path(self.test_memory_file).unlink()
    
    def test_memory_initialization(self):
        """Test memory system initialization."""
        self.assertIsNotNone(self.layout_memory.current_state)
        self.assertIn("panels", self.layout_memory.current_state)
        self.assertIn("current_sequence", self.layout_memory.current_state)
        self.assertIn("sequence_metadata", self.layout_memory.current_state)
    
    def test_add_panel_state(self):
        """Test adding panel state to memory."""
        panel_data = {
            "background": "forest setting",
            "lighting": "natural daylight",
            "camera_angle": "medium_shot",
            "character_position": "center"
        }
        
        self.layout_memory.add_panel_state(panel_data)
        
        self.assertEqual(len(self.layout_memory.current_state["panels"]), 1)
        self.assertEqual(self.layout_memory.current_state["panels"][0]["background"], "forest setting")
    
    def test_get_previous_panel_state(self):
        """Test retrieving previous panel state."""
        # Initially no previous state
        self.assertIsNone(self.layout_memory.get_previous_panel_state())
        
        # Add a panel
        panel_data = {"background": "urban setting", "lighting": "night lighting"}
        self.layout_memory.add_panel_state(panel_data)
        
        # Now should return the panel
        previous = self.layout_memory.get_previous_panel_state()
        self.assertIsNotNone(previous)
        self.assertEqual(previous["background"], "urban setting")
    
    def test_reset_sequence(self):
        """Test sequence reset functionality."""
        # Add some panels
        self.layout_memory.add_panel_state({"background": "test1"})
        self.layout_memory.add_panel_state({"background": "test2"})
        
        # Reset sequence
        self.layout_memory.reset_sequence("test_sequence")
        
        # Should be empty again
        self.assertEqual(len(self.layout_memory.current_state["panels"]), 0)
        self.assertEqual(self.layout_memory.current_state["sequence_metadata"]["sequence_id"], "test_sequence")


class TestCompositionRulesEngine(unittest.TestCase):
    """Test the CompositionRulesEngine class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_rules_file = "test_layout_rules.yaml"
        self.rules_engine = CompositionRulesEngine(self.test_rules_file)
    
    def tearDown(self):
        """Clean up test files."""
        if Path(self.test_rules_file).exists():
            Path(self.test_rules_file).unlink()
    
    def test_rules_initialization(self):
        """Test rules engine initialization."""
        self.assertIsNotNone(self.rules_engine.rules)
        self.assertIn("background_continuity", self.rules_engine.rules)
        self.assertIn("character_stability", self.rules_engine.rules)
        self.assertIn("lighting_rules", self.rules_engine.rules)
    
    def test_get_continuity_rules(self):
        """Test retrieving continuity rules."""
        bg_rules = self.rules_engine.get_continuity_rules("background_continuity")
        self.assertIsInstance(bg_rules, list)
        self.assertGreater(len(bg_rules), 0)
    
    def test_should_preserve_element(self):
        """Test element preservation logic."""
        previous_state = {"background": "forest setting"}
        
        # Should preserve background without location change
        should_preserve = self.rules_engine.should_preserve_element(
            "background", previous_state, "The character looks around"
        )
        self.assertTrue(should_preserve)
        
        # Should not preserve background with location change
        should_preserve = self.rules_engine.should_preserve_element(
            "background", previous_state, "The character travels to the city"
        )
        self.assertFalse(should_preserve)


class TestPanelPromptEnhancer(unittest.TestCase):
    """Test the PanelPromptEnhancer class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_memory_file = "test_enhancer_memory.json"
        self.test_rules_file = "test_enhancer_rules.yaml"
        self.enhancer = PanelPromptEnhancer(self.test_memory_file, self.test_rules_file)
    
    def tearDown(self):
        """Clean up test files."""
        for file in [self.test_memory_file, self.test_rules_file]:
            if Path(file).exists():
                Path(file).unlink()
        
        # Clean up enhanced prompts directory
        if self.enhancer.enhanced_prompts_dir.exists():
            for file in self.enhancer.enhanced_prompts_dir.glob("*.json"):
                file.unlink()
    
    def test_enhance_prompt(self):
        """Test prompt enhancement functionality."""
        base_prompt = "A young samurai stands in a forest clearing"
        scene_description = "Establishing shot of peaceful forest setting"
        
        enhanced_prompt, metadata = self.enhancer.enhance_prompt(
            base_prompt, scene_description, panel_index=0
        )
        
        self.assertIsInstance(enhanced_prompt, str)
        self.assertIn(base_prompt, enhanced_prompt)
        self.assertIn("LAYOUT_MEMORY", enhanced_prompt)
        self.assertIsInstance(metadata, dict)
        self.assertIn("camera_angle", metadata)
        self.assertIn("background", metadata)
    
    def test_extract_layout_elements(self):
        """Test layout element extraction."""
        prompt = "Close-up shot of samurai in forest at night"
        scene_desc = "Dramatic lighting with moonlight"
        
        layout = self.enhancer._extract_layout_elements(prompt, scene_desc)
        
        self.assertEqual(layout["camera_angle"], "close_up")
        self.assertEqual(layout["background"], "forest setting")
        self.assertEqual(layout["lighting"], "night lighting")
        self.assertEqual(layout["time_of_day"], "night")
    
    def test_sequence_continuity(self):
        """Test continuity across multiple panels."""
        prompts = [
            "Samurai in forest clearing",
            "Samurai draws sword",
            "Close-up of samurai's face"
        ]
        
        enhanced_results = []
        for i, prompt in enumerate(prompts):
            enhanced_prompt, metadata = self.enhancer.enhance_prompt(prompt, panel_index=i)
            self.enhancer.update_panel_memory(metadata)
            enhanced_results.append((enhanced_prompt, metadata))
        
        # Check that continuity is maintained
        self.assertEqual(len(enhanced_results), 3)
        
        # Second panel should reference first panel's background
        second_enhanced = enhanced_results[1][0]
        self.assertIn("CONTINUITY", second_enhanced)


class TestAutoMetadataTracker(unittest.TestCase):
    """Test the AutoMetadataTracker class."""
    
    def setUp(self):
        """Set up test environment."""
        self.tracker = AutoMetadataTracker()
    
    def test_extract_panel_metadata(self):
        """Test metadata extraction from panel."""
        image_path = "test_panel.png"
        prompt_used = "Wide shot of samurai in urban setting at sunset"
        scene_description = "Character arrives in the city"
        
        metadata = self.tracker.extract_panel_metadata(image_path, prompt_used, scene_description)
        
        self.assertEqual(metadata["image_path"], image_path)
        self.assertEqual(metadata["detected_camera_angle"], "wide_shot")
        self.assertEqual(metadata["detected_background"], "urban setting")
        self.assertEqual(metadata["detected_lighting"], "sunset lighting")


class TestIntegrationFunctions(unittest.TestCase):
    """Test integration functions."""
    
    def test_enhance_manga_prompts(self):
        """Test enhancing multiple prompts."""
        prompts = [
            "Samurai in forest",
            "Samurai fighting enemies",
            "Samurai victorious"
        ]
        
        scene_descriptions = [
            "Peaceful forest scene",
            "Action battle scene", 
            "Triumphant ending scene"
        ]
        
        enhanced_results = enhance_manga_prompts(prompts, scene_descriptions, "test_manga")
        
        self.assertEqual(len(enhanced_results), 3)
        
        for enhanced_prompt, metadata in enhanced_results:
            self.assertIsInstance(enhanced_prompt, str)
            self.assertIsInstance(metadata, dict)
            self.assertIn("LAYOUT_MEMORY", enhanced_prompt)


def run_comprehensive_test():
    """Run a comprehensive test of the entire system."""
    print("🧪 Running Comprehensive Panel Prompt Enhancer Test")
    print("=" * 60)
    
    # Test story scenario
    story_prompts = [
        "A young ninja discovers an ancient scroll in a hidden temple",
        "The ninja reads the scroll and learns about a powerful technique",
        "Enemies attack the temple, forcing the ninja to defend",
        "The ninja uses the new technique to defeat the attackers",
        "The ninja escapes the temple with the sacred scroll"
    ]
    
    scene_descriptions = [
        "Wide establishing shot of mysterious temple interior",
        "Medium shot of ninja examining ancient text",
        "Dynamic action shot as enemies burst in",
        "Close-up of ninja's focused expression during technique",
        "Wide shot of ninja leaping from temple rooftop"
    ]
    
    print(f"📚 Testing story with {len(story_prompts)} panels...")
    
    # Enhance the prompts
    enhanced_results = enhance_manga_prompts(story_prompts, scene_descriptions, "comprehensive_test")
    
    print(f"\n📊 Enhancement Results:")
    for i, (enhanced_prompt, metadata) in enumerate(enhanced_results):
        print(f"\nPanel {i+1}:")
        print(f"  Original: {story_prompts[i]}")
        print(f"  Enhanced: {enhanced_prompt[:80]}...")
        print(f"  Layout: {metadata['camera_angle']} | {metadata['background']} | {metadata['lighting']}")
        print(f"  Continuity: {len(metadata.get('continuity_applied', []))} rules applied")
    
    print(f"\n✅ Comprehensive test completed successfully!")
    print(f"📁 Enhanced prompts saved to outputs/enhanced_prompts_phase12/")
    
    return enhanced_results


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Panel Prompt Enhancer")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive test")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    if args.unit or args.all:
        print("🧪 Running Unit Tests...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    
    if args.comprehensive or args.all:
        print("\n" + "="*60)
        run_comprehensive_test()
    
    if not any([args.unit, args.comprehensive, args.all]):
        print("Panel Prompt Enhancer Test Suite")
        print("Usage:")
        print("  --unit           Run unit tests")
        print("  --comprehensive  Run comprehensive test")
        print("  --all           Run all tests")
