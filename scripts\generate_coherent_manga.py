#!/usr/bin/env python3
"""
Coherent Manga Generator

Generates manga with enhanced coherence by addressing visual continuity, 
character consistency, and narrative flow issues.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.generate_quality_manga import QualityMangaGenerator


class CoherentMangaGenerator(QualityMangaGenerator):
    """Enhanced manga generator with coherence fixes."""
    
    def __init__(self):
        super().__init__()
        self.character_reference = None
        self.lighting_style = None
        self.art_style_base = "Traditional manga style with clean lines, detailed backgrounds, consistent character proportions"
        
    def build_coherent_image_prompts(self, story_scenes: List[str]) -> List[str]:
        """Build image prompts with enhanced coherence."""
        
        print("🎨 Building coherent image prompts...")
        
        # Define consistent character
        self.character_reference = "A young samurai warrior with traditional dark blue armor, silver katana, determined facial expression, black hair in topknot, athletic build"
        
        # Define consistent lighting progression
        lighting_progression = [
            "dark ominous atmosphere with storm clouds",  # Scene 1-3: Cursed village
            "misty mountain lighting with filtered sunlight",  # Scene 4: Mountain climb  
            "dramatic lightning and storm effects",  # Scene 5-6: Guardian battle
            "divine golden light from Crystal Sword",  # Scene 7-8: Temple
            "warm natural sunlight, peaceful atmosphere"  # Scene 9: Village restored
        ]
        
        coherent_prompts = []
        
        for i, scene in enumerate(story_scenes):
            scene_num = i + 1
            
            # Get appropriate lighting
            lighting_index = min(i // 2, len(lighting_progression) - 1)
            lighting = lighting_progression[lighting_index]
            
            # Build coherent prompt
            base_prompt = self.create_scene_specific_prompt(scene, scene_num)
            
            coherent_prompt = f"{base_prompt} | CHARACTER: {self.character_reference} | LIGHTING: {lighting} | STYLE: {self.art_style_base} | CONTINUITY: Maintain visual consistency with previous panels"
            
            coherent_prompts.append(coherent_prompt)
            print(f"   Scene {scene_num}: Enhanced with coherence fixes")
        
        return coherent_prompts
    
    def create_scene_specific_prompt(self, scene_description: str, scene_num: int) -> str:
        """Create scene-specific prompt with coherence considerations."""
        
        # Scene-specific enhancements
        scene_prompts = {
            1: "Cursed village with withered trees, abandoned houses, dark storm clouds overhead",
            2: "Same cursed village center, ancient stone monument with mystical symbols, mysterious sage with glowing eyes",
            3: "Cursed village setting continues, samurai kneeling respectfully before sage",
            4: "Mountain path beginning from village outskirts, rocky terrain, mist-covered peaks ahead",
            5: "Mountain peak temple entrance, massive stone guardian with red glowing eyes, lightning in stormy sky",
            6: "Epic battle at temple entrance, stone guardian vs samurai, magical energy effects, mountain setting",
            7: "Sacred temple interior, divine Crystal Sword floating in beam of light, ancient pillars",
            8: "Temple interior continues, samurai grasping Crystal Sword, magical energy radiating",
            9: "Village restored to life, green grass, happy villagers, samurai returning as hero"
        }
        
        base_scene = scene_prompts.get(scene_num, scene_description)
        
        # Add transition elements for continuity
        if scene_num > 1:
            if scene_num in [2, 3]:  # Village scenes
                base_scene += ", maintaining cursed village atmosphere from previous panel"
            elif scene_num == 4:  # Transition to mountain
                base_scene += ", showing path leading away from village toward mountains"
            elif scene_num in [5, 6]:  # Mountain/temple scenes
                base_scene += ", continuing mountain temple setting"
            elif scene_num in [7, 8]:  # Temple interior
                base_scene += ", maintaining sacred temple interior atmosphere"
            elif scene_num == 9:  # Return to village
                base_scene += ", showing the same village from beginning but now restored"
        
        return base_scene
    
    def generate_coherent_manga(self, story_prompt: str, output_dir: str = None) -> Dict[str, Any]:
        """Generate manga with enhanced coherence."""
        
        print(f"🎯 Generating coherent manga: {story_prompt}")
        
        # Create output directory
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"outputs/coherent_manga_{timestamp}"
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate story with enhanced descriptions
        print("📖 Generating enhanced story...")
        story_scenes = self.generate_enhanced_story(story_prompt)
        
        # Build coherent image prompts
        image_prompts = self.build_coherent_image_prompts(story_scenes)
        
        # Generate images with coherence
        print("🎨 Generating coherent images...")
        results = self.generate_coherent_images(image_prompts, story_scenes, output_path)
        
        # Add dialogue bubbles
        print("💬 Adding dialogue bubbles...")
        self.add_coherent_dialogue_bubbles(results, output_path)
        
        # Create final manga
        print("📚 Creating final coherent manga...")
        self.create_final_manga_pdf(results, output_path)
        
        # Save results
        results_file = output_path / "coherent_manga_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ Coherent manga generated: {output_path}")
        return results
    
    def generate_enhanced_story(self, story_prompt: str) -> List[str]:
        """Generate story with enhanced scene descriptions for coherence."""
        
        # Enhanced story template with coherence in mind
        enhanced_scenes = [
            f"A young samurai warrior stands at the edge of a cursed village. {story_prompt} begins here. Dark clouds gather overhead as withered trees and abandoned houses create an ominous atmosphere. The samurai wears traditional armor and carries a katana, looking determined despite the foreboding environment.",
            
            f"The samurai encounters a mysterious old sage in the village center. The sage has glowing eyes and wears tattered robes, standing near an ancient stone monument covered in mystical symbols. Magical energy swirls around them as the sage speaks of the Crystal Sword.",
            
            f"The samurai kneels before the sage in respect, showing humility and determination. The background shows the same cursed village with dead trees and dark sky, emphasizing the urgency of their quest to find the Crystal Sword.",
            
            f"The samurai begins climbing a treacherous mountain path that leads away from the village. Sharp rocks and steep cliffs surround them, with mist swirling around the peaks. The warrior shows strain but determination as they ascend toward their destiny.",
            
            f"At the mountain peak, the samurai faces a massive stone guardian with glowing red eyes. The guardian blocks the entrance to a sacred temple where the Crystal Sword awaits. Lightning crackles in the stormy sky above as the confrontation begins.",
            
            f"Epic battle scene as the samurai fights the stone guardian at the temple entrance. Dynamic action with sword clashes, magical energy, and debris flying. The warrior shows skill and courage against the massive opponent while lightning illuminates the scene.",
            
            f"Inside the sacred temple, the samurai approaches a pedestal where the Crystal Sword floats in a beam of pure light. Ancient pillars and mystical symbols surround the holy weapon, creating an atmosphere of divine power.",
            
            f"The samurai grasps the Crystal Sword, and it emits brilliant magical energy. The blade glows with power as mystical light fills the temple. The warrior's quest reaches its climax as they claim the legendary weapon.",
            
            f"The samurai returns to the village, now healed and restored. Green grass grows where once was barren earth, and villagers emerge to celebrate. The curse is broken, and the hero has completed their quest with the Crystal Sword at their side."
        ]
        
        return enhanced_scenes
    
    def generate_coherent_images(self, image_prompts: List[str], story_scenes: List[str], output_path: Path) -> Dict[str, Any]:
        """Generate images with coherence tracking."""
        
        results = {
            "story_prompt": "Coherent manga generation",
            "chapters": [],
            "total_scenes": len(image_prompts),
            "coherence_enhanced": True
        }
        
        # Organize into chapters (3 scenes each)
        scenes_per_chapter = 3
        chapter_num = 1
        
        for i in range(0, len(image_prompts), scenes_per_chapter):
            chapter_scenes = []
            chapter_path = output_path / f"chapter_{chapter_num:02d}"
            chapter_path.mkdir(exist_ok=True)
            
            for j in range(scenes_per_chapter):
                scene_idx = i + j
                if scene_idx >= len(image_prompts):
                    break
                
                scene_num = scene_idx + 1
                image_prompt = image_prompts[scene_idx]
                story_scene = story_scenes[scene_idx]
                
                # Generate image
                image_path = chapter_path / f"scene_{scene_num:02d}.png"
                
                print(f"   🎨 Generating Scene {scene_num} with coherence...")
                success = self.generate_image_with_retry(image_prompt, str(image_path))
                
                if success:
                    # Extract dialogue and emotion
                    dialogue = self.extract_dialogue_from_scene(story_scene)
                    emotion = self.determine_scene_emotion(story_scene, scene_num)
                    
                    scene_data = {
                        "scene_number": scene_num,
                        "description": story_scene,
                        "image_prompt": image_prompt,
                        "panel_path": str(image_path),
                        "dialogue": dialogue,
                        "emotion": emotion,
                        "coherence_enhanced": True
                    }
                    
                    chapter_scenes.append(scene_data)
                    print(f"      ✅ Scene {scene_num} generated with coherence")
                else:
                    print(f"      ❌ Scene {scene_num} generation failed")
            
            if chapter_scenes:
                results["chapters"].append({
                    "chapter_number": chapter_num,
                    "scenes": chapter_scenes
                })
                chapter_num += 1
        
        return results
    
    def determine_scene_emotion(self, scene_description: str, scene_num: int) -> str:
        """Determine emotion with coherence in mind."""
        
        # Coherent emotion progression
        emotion_progression = [
            "determined",      # Scene 1: Starting quest
            "mystical",        # Scene 2: Meeting sage
            "respectful",      # Scene 3: Learning from sage
            "determined",      # Scene 4: Climbing mountain
            "confrontational", # Scene 5: Facing guardian
            "intense",         # Scene 6: Battle
            "awestruck",       # Scene 7: Finding sword
            "triumphant",      # Scene 8: Claiming sword
            "joyful"          # Scene 9: Victory celebration
        ]
        
        if 1 <= scene_num <= len(emotion_progression):
            return emotion_progression[scene_num - 1]
        
        return "determined"  # Default
    
    def add_coherent_dialogue_bubbles(self, results: Dict[str, Any], output_path: Path):
        """Add dialogue bubbles with coherence considerations."""
        
        print("💬 Adding coherent dialogue bubbles...")
        
        for chapter in results["chapters"]:
            chapter_num = chapter["chapter_number"]
            
            for scene in chapter["scenes"]:
                if scene["dialogue"]:
                    scene_num = scene["scene_number"]
                    panel_path = scene["panel_path"]
                    dialogue = scene["dialogue"]
                    
                    # Create bubble panel
                    bubble_path = str(Path(panel_path).parent / f"scene_{scene_num:02d}_bubble.png")
                    
                    success = self.add_dialogue_bubble(panel_path, dialogue, bubble_path)
                    
                    if success:
                        scene["bubble_panel_path"] = bubble_path
                        print(f"   💬 Added dialogue to Scene {scene_num}")
                    else:
                        print(f"   ❌ Failed to add dialogue to Scene {scene_num}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Generate coherent manga with enhanced visual continuity",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/generate_coherent_manga.py "A robot discovers friendship"
    python scripts/generate_coherent_manga.py "A magical adventure" --output coherent_manga_test
        """
    )
    
    parser.add_argument(
        "story_prompt",
        help="Story prompt for manga generation"
    )
    
    parser.add_argument(
        "--output",
        help="Output directory (default: auto-generated)"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize coherent generator
        generator = CoherentMangaGenerator()
        
        # Generate coherent manga
        results = generator.generate_coherent_manga(args.story_prompt, args.output)
        
        print(f"\n🎉 Coherent manga generation complete!")
        print(f"📊 Generated {results['total_scenes']} scenes with enhanced coherence")
        print(f"📁 Output directory: {args.output or 'auto-generated'}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Coherent manga generation failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
