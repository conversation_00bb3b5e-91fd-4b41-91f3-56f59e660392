#!/usr/bin/env python3
"""
Coherence Comparison Runner
Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator

This script runs full coherence analysis comparing original vs enhanced layout panels.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.coherence_analyzer import CoherenceAnalyzer


class CoherenceComparisonRunner:
    """Runs coherence analysis comparison between original and enhanced panels."""
    
    def __init__(self, use_local_flow_checker: bool = True):
        """Initialize the comparison runner."""
        self.use_local_flow_checker = use_local_flow_checker
        self.output_dir = Path("outputs/coherence_comparison_phase13")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize analyzer with local flow checker
        self.analyzer = CoherenceAnalyzer(use_local_flow_checker=use_local_flow_checker)
        
    def find_panel_sets(self) -> Dict[str, Dict[str, List[str]]]:
        """Find original and enhanced panel sets for comparison."""
        
        print("🔍 Searching for panel sets to compare...")
        
        # Look for original panels (from previous phases)
        original_dirs = [
            "outputs/manga_panels",
            "outputs/panels", 
            "outputs/generated_panels"
        ]
        
        # Look for enhanced panels (from Phase 13)
        enhanced_dirs = [
            "outputs/layout_enhanced_panels"
        ]
        
        panel_sets = {}
        
        # Find original panels
        for orig_dir in original_dirs:
            orig_path = Path(orig_dir)
            if orig_path.exists():
                panels = sorted([str(p) for p in orig_path.glob("*.png")])
                if panels:
                    panel_sets["original"] = {
                        "directory": str(orig_path),
                        "panels": panels,
                        "count": len(panels)
                    }
                    print(f"   📁 Found {len(panels)} original panels in {orig_dir}")
                    break
        
        # Find enhanced panels
        for enh_dir in enhanced_dirs:
            enh_path = Path(enh_dir)
            if enh_path.exists():
                panels = sorted([str(p) for p in enh_path.glob("*.png")])
                if panels:
                    panel_sets["enhanced"] = {
                        "directory": str(enh_path),
                        "panels": panels,
                        "count": len(panels)
                    }
                    print(f"   📁 Found {len(panels)} enhanced panels in {enh_dir}")
                    break
        
        if not panel_sets:
            print("   ❌ No panel sets found for comparison")
        elif len(panel_sets) == 1:
            print("   ⚠️  Only one panel set found - need both original and enhanced")
        else:
            print(f"   ✅ Found {len(panel_sets)} panel sets for comparison")
        
        return panel_sets
    
    def create_scene_data(self, panel_paths: List[str], set_name: str) -> List[Dict[str, Any]]:
        """Create scene data for coherence analysis."""
        
        # Create basic scene data based on panel count
        scene_data = []
        
        for i, panel_path in enumerate(panel_paths):
            scene = {
                "description": f"{set_name} manga panel {i+1}",
                "dialogue": "",  # Would need to extract from actual data
                "emotion": "neutral",  # Default emotion
                "panel_index": i,
                "panel_path": panel_path
            }
            scene_data.append(scene)
        
        return scene_data
    
    def analyze_panel_set(self, panel_paths: List[str], set_name: str) -> Dict[str, Any]:
        """Analyze coherence for a single panel set."""
        
        print(f"📊 Analyzing {set_name} panel set ({len(panel_paths)} panels)...")
        
        if len(panel_paths) < 2:
            return {
                "error": f"Need at least 2 panels for analysis, found {len(panel_paths)}",
                "set_name": set_name,
                "panel_count": len(panel_paths)
            }
        
        # Create scene data
        scene_data = self.create_scene_data(panel_paths, set_name)
        
        # Run coherence analysis
        try:
            analysis_result = self.analyzer.analyze_sequence_coherence(panel_paths, scene_data)
            
            # Add metadata
            analysis_result.update({
                "set_name": set_name,
                "panel_count": len(panel_paths),
                "analysis_timestamp": datetime.now().isoformat(),
                "local_flow_checker_used": self.use_local_flow_checker
            })
            
            # Extract key metrics
            coherence_score = analysis_result.get("coherence_score", 0.0)
            overall_assessment = analysis_result.get("overall_assessment", "unknown")
            analysis_method = analysis_result.get("detailed_analysis", {}).get("visual_analysis", {}).get("analysis_method", "unknown")
            
            print(f"   📈 Coherence score: {coherence_score:.3f}")
            print(f"   🎯 Assessment: {overall_assessment}")
            print(f"   🔧 Analysis method: {analysis_method}")
            
            return analysis_result
            
        except Exception as e:
            print(f"   ❌ Analysis failed: {e}")
            return {
                "error": f"Analysis failed: {e}",
                "set_name": set_name,
                "panel_count": len(panel_paths),
                "coherence_score": 0.0
            }
    
    def compare_results(self, original_result: Dict[str, Any], enhanced_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare original vs enhanced analysis results."""
        
        print("📋 Comparing analysis results...")
        
        # Extract scores
        orig_score = original_result.get("coherence_score", 0.0)
        enh_score = enhanced_result.get("coherence_score", 0.0)
        
        # Calculate improvement
        score_delta = enh_score - orig_score
        percent_improvement = (score_delta / orig_score * 100) if orig_score > 0 else 0
        
        # Extract analysis methods
        orig_method = original_result.get("detailed_analysis", {}).get("visual_analysis", {}).get("analysis_method", "unknown")
        enh_method = enhanced_result.get("detailed_analysis", {}).get("visual_analysis", {}).get("analysis_method", "unknown")
        
        # Determine improvement category
        if score_delta >= 0.1:
            improvement_category = "significant_improvement"
        elif score_delta >= 0.05:
            improvement_category = "moderate_improvement"
        elif score_delta >= -0.05:
            improvement_category = "unchanged"
        elif score_delta >= -0.1:
            improvement_category = "moderate_decline"
        else:
            improvement_category = "significant_decline"
        
        comparison = {
            "comparison_timestamp": datetime.now().isoformat(),
            "scores": {
                "original": orig_score,
                "enhanced": enh_score,
                "delta": score_delta,
                "percent_improvement": percent_improvement
            },
            "analysis_methods": {
                "original": orig_method,
                "enhanced": enh_method,
                "local_flow_checker_coverage": enh_method == "local_opencv"
            },
            "improvement_category": improvement_category,
            "assessments": {
                "original": original_result.get("overall_assessment", "unknown"),
                "enhanced": enhanced_result.get("overall_assessment", "unknown")
            },
            "panel_counts": {
                "original": original_result.get("panel_count", 0),
                "enhanced": enhanced_result.get("panel_count", 0)
            },
            "success_criteria": {
                "coherence_improved": score_delta > 0,
                "local_flow_used": enh_method == "local_opencv",
                "meets_70_percent_threshold": percent_improvement >= 70 if orig_score > 0 else enh_score >= 0.7
            }
        }
        
        print(f"   📊 Score change: {orig_score:.3f} → {enh_score:.3f} (Δ{score_delta:+.3f})")
        print(f"   📈 Improvement: {percent_improvement:+.1f}%")
        print(f"   🏷️  Category: {improvement_category}")
        print(f"   🔧 Local flow used: {enh_method == 'local_opencv'}")
        
        return comparison
    
    def save_results(self, original_result: Dict[str, Any], enhanced_result: Dict[str, Any], 
                    comparison: Dict[str, Any]) -> Dict[str, str]:
        """Save all analysis results to files."""
        
        print("💾 Saving analysis results...")
        
        # Save individual results
        orig_file = self.output_dir / "original_scores.json"
        enh_file = self.output_dir / "enhanced_scores.json"
        comp_file = self.output_dir / "comparison_results.json"
        
        with open(orig_file, 'w', encoding='utf-8') as f:
            json.dump(original_result, f, indent=2)
        
        with open(enh_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_result, f, indent=2)
        
        with open(comp_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, indent=2)
        
        print(f"   📁 Original results: {orig_file}")
        print(f"   📁 Enhanced results: {enh_file}")
        print(f"   📁 Comparison: {comp_file}")
        
        return {
            "original_scores": str(orig_file),
            "enhanced_scores": str(enh_file),
            "comparison_results": str(comp_file)
        }
    
    def run_full_comparison(self) -> Dict[str, Any]:
        """Run complete coherence comparison analysis."""
        
        print("Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator")
        print("Task 4: Run Full Coherence Analysis")
        print("=" * 70)
        
        # Find panel sets
        panel_sets = self.find_panel_sets()
        
        if len(panel_sets) < 2:
            return {
                "error": "Need both original and enhanced panel sets for comparison",
                "found_sets": list(panel_sets.keys()),
                "success": False
            }
        
        # Analyze original panels
        original_result = self.analyze_panel_set(
            panel_sets["original"]["panels"], 
            "original"
        )
        
        # Analyze enhanced panels
        enhanced_result = self.analyze_panel_set(
            panel_sets["enhanced"]["panels"], 
            "enhanced"
        )
        
        # Compare results
        comparison = self.compare_results(original_result, enhanced_result)
        
        # Save results
        file_paths = self.save_results(original_result, enhanced_result, comparison)
        
        # Final summary
        success_criteria = comparison.get("success_criteria", {})
        local_flow_coverage = comparison.get("analysis_methods", {}).get("local_flow_checker_coverage", False)
        
        print(f"\n📊 Phase 13 Analysis Summary:")
        print(f"   ✅ Coherence improved: {success_criteria.get('coherence_improved', False)}")
        print(f"   🔧 Local flow coverage: {local_flow_coverage}")
        print(f"   🎯 Meets criteria: {success_criteria.get('meets_70_percent_threshold', False)}")
        
        return {
            "success": True,
            "original_analysis": original_result,
            "enhanced_analysis": enhanced_result,
            "comparison": comparison,
            "file_paths": file_paths,
            "summary": {
                "coherence_improved": success_criteria.get("coherence_improved", False),
                "local_flow_coverage": local_flow_coverage,
                "meets_success_criteria": success_criteria.get("meets_70_percent_threshold", False)
            }
        }


def main():
    """Main comparison function."""
    
    parser = argparse.ArgumentParser(description="Coherence Comparison Analysis")
    parser.add_argument("--no-local-flow", action="store_true",
                       help="Disable local flow checker (VLM only)")
    parser.add_argument("--original-dir", type=str,
                       help="Directory containing original panels")
    parser.add_argument("--enhanced-dir", type=str,
                       help="Directory containing enhanced panels")
    
    args = parser.parse_args()
    
    # Initialize runner
    use_local_flow = not args.no_local_flow
    runner = CoherenceComparisonRunner(use_local_flow_checker=use_local_flow)
    
    # Override directories if specified
    if args.original_dir or args.enhanced_dir:
        print("⚠️  Custom directory specification not yet implemented")
        print("    Using automatic panel discovery")
    
    # Run comparison
    results = runner.run_full_comparison()
    
    if results.get("success", False):
        summary = results.get("summary", {})
        if summary.get("meets_success_criteria", False):
            print("\n🎉 Phase 13 SUCCESS: Enhanced layout shows significant improvement!")
            return 0
        else:
            print("\n⚠️  Phase 13 PARTIAL: Some improvements but below success threshold")
            return 1
    else:
        print(f"\n❌ Phase 13 FAILED: {results.get('error', 'Unknown error')}")
        return 2


if __name__ == "__main__":
    exit(main())
