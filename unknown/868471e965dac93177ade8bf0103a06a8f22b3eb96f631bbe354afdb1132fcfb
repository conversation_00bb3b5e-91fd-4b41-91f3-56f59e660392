#!/usr/bin/env python3
"""
Dialogue Bubble Overlay Script

Places dialogue bubbles on manga panels with automatic positioning based on character faces.
"""

import cv2
from pathlib import Path
from typing import List
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load face cascade (local OpenCV XML file)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')


def detect_face_bbox(img):
    """
    Detect face bounding box in image.

    Args:
        img: OpenCV image array

    Returns:
        Tuple of (x, y, w, h) for largest face, or None if no face found
    """
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5)
    if len(faces) == 0:
        return None
    # Return the largest face
    x, y, w, h = max(faces, key=lambda f: f[2] * f[3])
    return (x, y, w, h)


# Removed unused PIL-based functions - using OpenCV implementation instead


def place_dialogue(panel_path: str, dialogue_lines: List[str], output_path: str) -> bool:
    """
    Place dialogue bubbles on a manga panel using OpenCV.

    Args:
        panel_path: Path to the original panel image
        dialogue_lines: List of dialogue lines
        output_path: Path to save the panel with dialogue bubbles

    Returns:
        True if successful, False otherwise
    """
    try:
        if not dialogue_lines or not any(line.strip() for line in dialogue_lines):
            # No dialogue to add, just copy the original
            import shutil
            shutil.copy2(panel_path, output_path)
            return True

        # Load image with OpenCV
        img = cv2.imread(panel_path)
        if img is None:
            print(f"❌ Could not load image: {panel_path}")
            return False

        h, w, _ = img.shape
        face = detect_face_bbox(img)

        # Decide bubble position
        if face:
            fx, fy, fw, fh = face
            # If face center is on right half, bubble on left; else on right
            bubble_x = int(w * 0.05) if (fx + fw / 2) > w / 2 else int(w * 0.5)
            bubble_y = int(h * 0.05)
        else:
            # Default top-left if no face found
            bubble_x, bubble_y = int(w * 0.05), int(h * 0.05)

        # Prepare text and font settings
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        line_spacing = 5
        pad = 15

        # Calculate text dimensions for each line
        line_sizes = []
        max_line_width = 0
        total_text_height = 0

        for line in dialogue_lines:
            if line.strip():  # Skip empty lines
                (text_w, text_h), baseline = cv2.getTextSize(line.strip(), font, font_scale, thickness)
                line_sizes.append((text_w, text_h, baseline))
                max_line_width = max(max_line_width, text_w)
                total_text_height += text_h + line_spacing

        # Remove last line spacing
        if line_sizes:
            total_text_height -= line_spacing

        # Calculate bubble dimensions
        bubble_w = max_line_width + pad * 2
        bubble_h = total_text_height + pad * 2

        # Ensure bubble fits within image bounds
        if bubble_x + bubble_w > w:
            bubble_x = w - bubble_w - 10
        if bubble_y + bubble_h > h:
            bubble_y = h - bubble_h - 10

        # Ensure minimum position
        bubble_x = max(10, bubble_x)
        bubble_y = max(10, bubble_y)

        # Draw filled white rectangle for bubble
        cv2.rectangle(
            img,
            (bubble_x, bubble_y),
            (bubble_x + bubble_w, bubble_y + bubble_h),
            (255, 255, 255), -1  # filled white
        )

        # Draw black border around bubble
        cv2.rectangle(
            img,
            (bubble_x, bubble_y),
            (bubble_x + bubble_w, bubble_y + bubble_h),
            (0, 0, 0), 2  # black border
        )

        # Draw each line of text separately
        current_y = bubble_y + pad
        for i, line in enumerate(dialogue_lines):
            if line.strip() and i < len(line_sizes):  # Skip empty lines
                text_w, text_h, baseline = line_sizes[i]
                current_y += text_h

                # Center text horizontally in bubble
                text_x = bubble_x + (bubble_w - text_w) // 2

                cv2.putText(
                    img,
                    line.strip(),
                    (text_x, current_y),
                    font,
                    font_scale,
                    (0, 0, 0),
                    thickness
                )
                current_y += line_spacing

        # Save the result
        cv2.imwrite(output_path, img)
        print(f"✅ Added dialogue to panel: {output_path}")
        return True

    except Exception as e:
        print(f"❌ Error placing dialogue on {panel_path}: {e}")
        # Copy original as fallback
        try:
            import shutil
            shutil.copy2(panel_path, output_path)
        except:
            pass
        return False


def main():
    """Test the dialogue placement functionality."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Add dialogue bubbles to manga panels")
    parser.add_argument("panel_path", help="Path to the panel image")
    parser.add_argument("--dialogue", nargs="+", help="Dialogue lines to add")
    parser.add_argument("--output", help="Output path (default: panel_with_dialogue.png)")
    
    args = parser.parse_args()
    
    output_path = args.output or "panel_with_dialogue.png"
    dialogue = args.dialogue or ["Hello!", "How are you?"]
    
    success = place_dialogue(args.panel_path, dialogue, output_path)
    
    if success:
        print(f"✅ Dialogue added successfully: {output_path}")
        return 0
    else:
        print(f"❌ Failed to add dialogue")
        return 1


if __name__ == "__main__":
    exit(main())
