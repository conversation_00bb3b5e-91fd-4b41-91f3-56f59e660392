# MangaGen Cleanup & Restructure Plan

## Current Active Dependencies (KEEP)

### Core Pipeline Files
- `generate_panel.py` - ComfyUI integration (ACTIVE)
- `phase13_real_validation.py` - Real validation framework (ACTIVE)
- `run_phase13_real_validation.py` - Complete validation pipeline (ACTIVE)
- `base_prompts.txt` - Base prompts for validation (ACTIVE)
- `enhanced_prompts.txt` - Enhanced prompts for validation (ACTIVE)

### Core Modules (KEEP)
- `scripts/emotion_extractor.py` - Phase 7 emotion extraction (ACTIVE)
- `scripts/local_flow_checker.py` - Local OpenCV analysis (ACTIVE)
- `scripts/coherence_analyzer.py` - Coherence analysis (ACTIVE)
- `pipeline/` - Core pipeline modules (ACTIVE)
- `llm/` - LLM integration (ACTIVE)
- `image_gen/` - Image generation modules (ACTIVE)

### Configuration Files (KEEP)
- `comfy_workflows/manga_graph.json` - ComfyUI workflow (ACTIVE)
- `requirements.txt` - Dependencies (ACTIVE)
- `README.md` - Documentation (ACTIVE)

### Active Output Directories (KEEP)
- `outputs/base_panels/` - Generated base panels (ACTIVE)
- `outputs/enhanced_panels/` - Generated enhanced panels (ACTIVE)
- `outputs/phase13_real_validation/` - Real validation results (ACTIVE)
- `outputs/validation_results/` - Validation reports (ACTIVE)
- `outputs/emotion_outputs/` - Emotion extraction results (ACTIVE)

## Files to Remove (UNUSED/OBSOLETE)

### Test/Experimental Files (DELETE)
- `test_local_checker_minimal.py` - Minimal test script
- `test_phase13.py` - Phase 13 test script
- `test_phase13_framework.py` - Framework test script
- `extract_real_prompts.py` - Prompt extraction utility
- `phase_audit_7_13.py` - Audit script

### Documentation Files (DELETE - outdated)
- `PHASE12_SUMMARY.md` - Outdated summary
- `PHASE13_HONEST_STATUS.md` - Outdated status
- `PHASE13_INTEGRATION_TEST_LOG.md` - Test log
- `PHASE13_SUMMARY.md` - Outdated summary

### Obsolete Config Files (DELETE)
- `layout_memory.json` - Old layout config
- `layout_rules.yaml` - Old layout rules

### Obsolete Output Directories (DELETE)
- `outputs/coherence_eval_20250601_210543/` - Old evaluation
- `outputs/manga_20250601_193354/` - Old manga generation
- `outputs/manga_panels/` - Old panels (replaced by base/enhanced)
- `outputs/quality_failures/` - Old failure logs
- `outputs/quality_manga_*` - Old quality tests (multiple dirs)
- `outputs/test_panels_original/` - Old test panels
- `outputs/enhanced_prompts_phase12/` - Old enhanced prompts
- `outputs/phase_audit_7_13/` - Audit results
- `outputs/test_flow_analysis.json` - Test analysis
- `outputs/test_output.png` - Test output

### Obsolete Scripts (DELETE)
- `scripts/bias_calibrator.py` - Unused calibrator
- `scripts/cleanup_outputs.py` - Old cleanup script
- `scripts/coherence_auto_fix.py` - Auto-fix (replaced)
- `scripts/demo_*.py` - Demo scripts
- `scripts/eval_coherence.py` - Old evaluation
- `scripts/fix_manga_coherence.py` - Old fix script
- `scripts/generate_*.py` - Multiple old generators
- `scripts/narrative_thread_tracker.py` - Unused tracker
- `scripts/panel_prompt_enhancer.py` - Old enhancer
- `scripts/place_dialogue.py` - Old dialogue placement
- `scripts/regenerate_enhanced_manga.py` - Old regeneration
- `scripts/run_coherence_comparison.py` - Old comparison
- `scripts/self_test.py` - Self test script
- `scripts/test_*.py` - Test scripts
- `scripts/visual_qa_agent.py` - Unused VQA agent

## New Directory Structure

```
MangaGen/
├── core/                          # Core pipeline modules
│   ├── __init__.py
│   ├── emotion_extractor.py       # From scripts/
│   ├── local_flow_checker.py      # From scripts/
│   ├── coherence_analyzer.py      # From scripts/
│   └── validation/                # Validation framework
│       ├── __init__.py
│       ├── phase13_validator.py   # From phase13_real_validation.py
│       └── validation_runner.py   # From run_phase13_real_validation.py
├── scripts/                       # Entry points and CLI tools
│   ├── generate_panel.py          # ComfyUI integration
│   └── run_validation.py          # Validation entry point
├── pipeline/                      # Existing pipeline (keep as-is)
├── llm/                          # Existing LLM modules (keep as-is)
├── image_gen/                    # Existing image gen (keep as-is)
├── assets/                       # Configuration and templates
│   ├── workflows/
│   │   └── manga_graph.json      # From comfy_workflows/
│   ├── prompts/
│   │   ├── base_prompts.txt
│   │   └── enhanced_prompts.txt
│   └── configs/
├── outputs/                      # Generated content (cleaned)
│   ├── panels/
│   │   ├── base/                 # From base_panels/
│   │   └── enhanced/             # From enhanced_panels/
│   ├── validation/               # From validation_results/
│   ├── emotions/                 # From emotion_outputs/
│   └── reports/                  # Validation reports
├── tests/                        # Future tests
├── requirements.txt
└── README.md
```
