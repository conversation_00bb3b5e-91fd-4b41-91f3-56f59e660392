# Environment Variables for Manga Generation Pipeline
# Copy this file to .env and fill in your actual values

# =============================================================================
# LLM API Configuration
# =============================================================================

# OpenRouter API Key (for DeepSeek R1 story generation - FREE)
# Get your key from: https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Default model: DeepSeek Chat (100% FREE)
LLM_MODEL=deepseek/deepseek-chat

# =============================================================================
# ComfyUI Configuration
# =============================================================================

# ComfyUI Server URL
COMFYUI_URL=http://127.0.0.1:8188

# ComfyUI API timeout (seconds)
COMFYUI_TIMEOUT=300

# ComfyUI workflow directory (optional)
COMFYUI_WORKFLOWS_DIR=./workflows

# =============================================================================
# Image Generation Settings
# =============================================================================

# Default image dimensions
IMAGE_WIDTH=512
IMAGE_HEIGHT=768

# Stable Diffusion model settings
SD_MODEL_PATH=your_model_path_here
SD_VAE_PATH=your_vae_path_here

# =============================================================================
# Output Configuration
# =============================================================================

# Output directories
OUTPUT_DIR=./outputs
IMAGES_DIR=./outputs
MANGA_DIR=./outputs
TEMP_DIR=./temp

# =============================================================================
# Pipeline Settings
# =============================================================================

# Default generation settings
DEFAULT_GENRE=shonen
DEFAULT_ACTS=3
DEFAULT_SCENES_PER_ACT=3
PANELS_PER_PAGE=4

# Random seed (-1 for random)
RANDOM_SEED=-1

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Log file path (optional)
LOG_FILE=./logs/manga_generator.log
