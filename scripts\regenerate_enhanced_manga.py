#!/usr/bin/env python3
"""
Enhanced Manga Regeneration Script
Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator

This script regenerates manga panels using the layout-enhanced prompts from Phase 12.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from image_gen.image_generator import generate_image
from image_gen.comfy_client import ComfyUIClient


class EnhancedMangaRegenerator:
    """Regenerates manga panels using enhanced prompts from Phase 12."""
    
    def __init__(self, enhanced_prompts_dir: str = "outputs/enhanced_prompts_phase12"):
        """Initialize the regenerator."""
        self.enhanced_prompts_dir = Path(enhanced_prompts_dir)
        self.output_dir = Path("outputs/layout_enhanced_panels")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize ComfyUI client
        self.comfy_client = ComfyUIClient()
        
    def load_enhanced_prompts(self) -> List[Dict[str, Any]]:
        """Load all enhanced prompts from Phase 12."""
        
        print(f"📂 Loading enhanced prompts from {self.enhanced_prompts_dir}")
        
        prompt_files = sorted(self.enhanced_prompts_dir.glob("panel_*.json"))
        enhanced_prompts = []
        
        for prompt_file in prompt_files:
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    prompt_data = json.load(f)
                    enhanced_prompts.append(prompt_data)
                    print(f"   ✅ Loaded: {prompt_file.name}")
            except Exception as e:
                print(f"   ❌ Failed to load {prompt_file.name}: {e}")
        
        print(f"📊 Loaded {len(enhanced_prompts)} enhanced prompts")
        return enhanced_prompts
    
    def regenerate_panel(self, prompt_data: Dict[str, Any], panel_index: int) -> Optional[str]:
        """
        Regenerate a single panel using enhanced prompt.
        
        Args:
            prompt_data: Enhanced prompt data from Phase 12
            panel_index: Panel index for filename
            
        Returns:
            Path to generated image or None if failed
        """
        
        enhanced_prompt = prompt_data.get("enhanced_prompt", "")
        if not enhanced_prompt:
            print(f"   ❌ No enhanced prompt found for panel {panel_index}")
            return None
        
        print(f"🎨 Regenerating panel {panel_index + 1}...")
        print(f"   📝 Enhanced prompt: {enhanced_prompt[:100]}...")
        
        try:
            # Generate image using enhanced prompt
            image_path = generate_image(
                prompt=enhanced_prompt,
                index=panel_index + 1,
                output_dir=str(self.output_dir)
            )
            
            if image_path and Path(image_path).exists():
                print(f"   ✅ Generated: {image_path}")
                return image_path
            else:
                print(f"   ❌ Generation failed for panel {panel_index}")
                return None
                
        except Exception as e:
            print(f"   ❌ Error generating panel {panel_index}: {e}")
            return None
    
    def regenerate_sequence(self, enhanced_prompts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Regenerate complete manga sequence using enhanced prompts.
        
        Args:
            enhanced_prompts: List of enhanced prompt data
            
        Returns:
            Generation results and metadata
        """
        
        print(f"🚀 Starting enhanced manga regeneration...")
        print(f"📊 Panels to regenerate: {len(enhanced_prompts)}")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_panels": len(enhanced_prompts),
            "generated_panels": [],
            "failed_panels": [],
            "output_directory": str(self.output_dir),
            "source_prompts_dir": str(self.enhanced_prompts_dir)
        }
        
        # Sort prompts by panel index
        sorted_prompts = sorted(enhanced_prompts, key=lambda x: x.get("panel_index", 0))
        
        for i, prompt_data in enumerate(sorted_prompts):
            panel_index = prompt_data.get("panel_index", i)
            
            # Regenerate panel
            image_path = self.regenerate_panel(prompt_data, panel_index)
            
            if image_path:
                panel_result = {
                    "panel_index": panel_index,
                    "image_path": image_path,
                    "enhanced_prompt": prompt_data.get("enhanced_prompt", ""),
                    "base_prompt": prompt_data.get("base_prompt", ""),
                    "layout_metadata": prompt_data.get("layout_metadata", {}),
                    "generation_timestamp": datetime.now().isoformat()
                }
                results["generated_panels"].append(panel_result)
            else:
                results["failed_panels"].append({
                    "panel_index": panel_index,
                    "error": "Generation failed",
                    "prompt_data": prompt_data
                })
        
        # Save results
        results_file = self.output_dir / "regeneration_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 Regeneration Summary:")
        print(f"   ✅ Successfully generated: {len(results['generated_panels'])} panels")
        print(f"   ❌ Failed: {len(results['failed_panels'])} panels")
        print(f"   📁 Output directory: {self.output_dir}")
        print(f"   💾 Results saved: {results_file}")
        
        return results
    
    def create_panel_comparison(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create comparison data between original and enhanced panels.
        
        Args:
            results: Regeneration results
            
        Returns:
            Comparison metadata
        """
        
        print(f"\n📋 Creating panel comparison data...")
        
        comparison_data = {
            "timestamp": datetime.now().isoformat(),
            "enhanced_panels": results["generated_panels"],
            "comparison_notes": {
                "enhanced_prompts_used": True,
                "layout_memory_applied": True,
                "composition_rules_applied": True,
                "source": "Phase 12 layout enhancer"
            },
            "ready_for_coherence_analysis": True
        }
        
        # Save comparison data
        comparison_file = self.output_dir / "panel_comparison.json"
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, indent=2)
        
        print(f"   💾 Comparison data saved: {comparison_file}")
        return comparison_data


def main():
    """Main regeneration function."""
    
    parser = argparse.ArgumentParser(description="Enhanced Manga Regeneration")
    parser.add_argument("--prompts-dir", type=str, default="outputs/enhanced_prompts_phase12",
                       help="Directory containing enhanced prompts")
    parser.add_argument("--output-dir", type=str, default="outputs/layout_enhanced_panels",
                       help="Output directory for regenerated panels")
    parser.add_argument("--dry-run", action="store_true",
                       help="Load prompts but don't generate images")
    
    args = parser.parse_args()
    
    print("Phase 13: Enhanced Prompt Testing + Local Visual Flow Validator")
    print("Task 1: Prompt-Based Manga Regeneration")
    print("=" * 70)
    
    # Initialize regenerator
    regenerator = EnhancedMangaRegenerator(args.prompts_dir)
    if args.output_dir != "outputs/layout_enhanced_panels":
        regenerator.output_dir = Path(args.output_dir)
        regenerator.output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load enhanced prompts
    enhanced_prompts = regenerator.load_enhanced_prompts()
    
    if not enhanced_prompts:
        print("❌ No enhanced prompts found. Please run Phase 12 first.")
        return 1
    
    if args.dry_run:
        print(f"\n🔍 Dry run mode - would regenerate {len(enhanced_prompts)} panels")
        for i, prompt_data in enumerate(enhanced_prompts):
            enhanced_prompt = prompt_data.get("enhanced_prompt", "")
            print(f"   Panel {i+1}: {enhanced_prompt[:80]}...")
        return 0
    
    # Regenerate manga sequence
    results = regenerator.regenerate_sequence(enhanced_prompts)
    
    # Create comparison data
    comparison_data = regenerator.create_panel_comparison(results)
    
    # Final summary
    success_rate = len(results["generated_panels"]) / results["total_panels"] * 100
    print(f"\n🎉 Enhanced manga regeneration completed!")
    print(f"📊 Success rate: {success_rate:.1f}%")
    print(f"🚀 Ready for Phase 13 coherence analysis")
    
    return 0 if success_rate > 50 else 1


if __name__ == "__main__":
    exit(main())
