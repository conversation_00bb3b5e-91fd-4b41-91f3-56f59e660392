#!/usr/bin/env python3
"""
ComfyUI Panel Generation Script
Generates manga panels using local ComfyUI instance.
"""

import json
import time
import requests
import argparse
from pathlib import Path
from typing import Optional

class ComfyUIGenerator:
    """Interface for ComfyUI panel generation."""
    
    def __init__(self, comfyui_url: str = "http://127.0.0.1:8188"):
        """Initialize ComfyUI generator."""
        self.comfyui_url = comfyui_url
        self.api_url = f"{comfyui_url}/api"
        
    def check_comfyui_status(self) -> bool:
        """Check if ComfyUI is running and accessible."""
        try:
            response = requests.get(f"{self.api_url}/system_stats", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def create_workflow(self, prompt: str, output_path: str) -> dict:
        """Create ComfyUI workflow for manga panel generation."""
        
        # Basic ComfyUI workflow for text-to-image
        workflow = {
            "1": {
                "inputs": {
                    "text": prompt,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "2": {
                "inputs": {
                    "text": "blurry, low quality, distorted, bad anatomy",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "3": {
                "inputs": {
                    "seed": 42,
                    "steps": 20,
                    "cfg": 7.0,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["1", 0],
                    "negative": ["2", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "sd_xl_base_1.0.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "width": 512,
                    "height": 768,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage"
            },
            "6": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "7": {
                "inputs": {
                    "filename_prefix": Path(output_path).stem,
                    "images": ["6", 0]
                },
                "class_type": "SaveImage"
            }
        }
        
        return workflow
    
    def queue_prompt(self, workflow: dict) -> Optional[str]:
        """Queue a prompt in ComfyUI and return the prompt ID."""
        try:
            data = {"prompt": workflow}
            response = requests.post(f"{self.api_url}/prompt", json=data)
            
            if response.status_code == 200:
                result = response.json()
                return result.get("prompt_id")
            else:
                print(f"❌ Failed to queue prompt: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error queuing prompt: {e}")
            return None
    
    def wait_for_completion(self, prompt_id: str, timeout: int = 300) -> bool:
        """Wait for prompt completion."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_url}/history/{prompt_id}")
                if response.status_code == 200:
                    history = response.json()
                    if prompt_id in history:
                        return True
                
                time.sleep(2)
            except:
                time.sleep(2)
        
        return False
    
    def generate_panel(self, prompt: str, output_path: str) -> bool:
        """Generate a single manga panel."""

        print(f"Generating panel: {Path(output_path).name}")
        print(f"   Prompt: {prompt[:100]}...")

        # Check ComfyUI status
        if not self.check_comfyui_status():
            print("ComfyUI not accessible. Please ensure ComfyUI is running.")
            return False
        
        # Create workflow
        workflow = self.create_workflow(prompt, output_path)
        
        # Queue prompt
        prompt_id = self.queue_prompt(workflow)
        if not prompt_id:
            return False
        
        print(f"   Queued with ID: {prompt_id}")

        # Wait for completion
        if self.wait_for_completion(prompt_id):
            print(f"   Generation complete")
            return True
        else:
            print(f"   Generation timed out")
            return False

def generate_panels_from_file(prompt_file: str, output_dir: str, prefix: str = "panel") -> bool:
    """Generate panels from a prompt file."""

    print(f"Generating panels from: {prompt_file}")
    print(f"Output directory: {output_dir}")
    
    # Read prompts
    prompt_path = Path(prompt_file)
    if not prompt_path.exists():
        print(f"❌ Prompt file not found: {prompt_file}")
        return False
    
    with open(prompt_path, 'r', encoding='utf-8') as f:
        prompts = [line.strip() for line in f.readlines() if line.strip()]
    
    if not prompts:
        print("No prompts found in file")
        return False
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Initialize generator
    generator = ComfyUIGenerator()
    
    # Generate panels
    success_count = 0
    for i, prompt in enumerate(prompts):
        output_file = output_path / f"{prefix}_{i+1:02d}.png"
        
        if generator.generate_panel(prompt, str(output_file)):
            success_count += 1
        else:
            print(f"Failed to generate panel {i+1}")

    print(f"\nGeneration Summary:")
    print(f"   Total prompts: {len(prompts)}")
    print(f"   Successful: {success_count}")
    print(f"   Failed: {len(prompts) - success_count}")
    
    return success_count == len(prompts)

def main():
    """Main generation function."""
    
    parser = argparse.ArgumentParser(description="Generate manga panels using ComfyUI")
    parser.add_argument("--prompt", type=str, help="Single prompt to generate")
    parser.add_argument("--output", type=str, help="Output file path")
    parser.add_argument("--prompt-file", type=str, help="File containing prompts")
    parser.add_argument("--output-dir", type=str, help="Output directory for batch generation")
    parser.add_argument("--prefix", type=str, default="panel", help="Filename prefix for batch generation")
    parser.add_argument("--comfyui-url", type=str, default="http://127.0.0.1:8188", help="ComfyUI URL")
    
    args = parser.parse_args()
    
    if args.prompt and args.output:
        # Single panel generation
        generator = ComfyUIGenerator(args.comfyui_url)
        success = generator.generate_panel(args.prompt, args.output)
        return 0 if success else 1
        
    elif args.prompt_file and args.output_dir:
        # Batch generation
        success = generate_panels_from_file(args.prompt_file, args.output_dir, args.prefix)
        return 0 if success else 1
        
    else:
        print("Please specify either:")
        print("   --prompt and --output for single generation")
        print("   --prompt-file and --output-dir for batch generation")
        return 1

if __name__ == "__main__":
    exit(main())
